'use client';

import React, { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { DynamicTemplateGenerator } from '@/components/admin/dynamic-template-generator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Sparkles, FileText, Users, Settings } from 'lucide-react';
import { toast } from 'sonner';

export default function TemplateGeneratorPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get URL parameters
  const procurementId = searchParams.get('procurementId');
  const stageType = searchParams.get('stageType') || 'announcement';
  const approvalWorkflowId = searchParams.get('approvalWorkflowId');

  const [isGenerating, setIsGenerating] = useState(false);

  const handleSaveTemplate = async (template: any) => {
    setIsGenerating(true);
    try {
      // Simulate API call to save template
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Saving template:', template);
      
      toast.success('Template generated successfully!', {
        description: `${template.name} has been created and is ready to use.`
      });
      
      // Redirect back to template management
      router.push('/admin/document-templates');
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to generate template', {
        description: 'Please try again or contact support if the issue persists.'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  Dynamic Template Generator
                </h1>
                <p className="text-sm text-gray-500">
                  Create intelligent document templates for procurement workflows
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 text-sm text-gray-500">
                <Sparkles className="h-4 w-4 text-blue-500" />
                AI-Powered
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Context Information */}
      {(procurementId || stageType || approvalWorkflowId) && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Generation Context
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                {procurementId && (
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-500" />
                    <span className="text-gray-600">Procurement:</span>
                    <span className="font-medium">{procurementId}</span>
                  </div>
                )}
                {stageType && (
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-green-500" />
                    <span className="text-gray-600">Stage:</span>
                    <span className="font-medium capitalize">{stageType}</span>
                  </div>
                )}
                {approvalWorkflowId && (
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4 text-purple-500" />
                    <span className="text-gray-600">Workflow:</span>
                    <span className="font-medium">{approvalWorkflowId}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Generator */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <DynamicTemplateGenerator
          procurementId={procurementId || undefined}
          stageType={stageType}
          approvalWorkflowId={approvalWorkflowId || undefined}
          onSave={handleSaveTemplate}
          onCancel={handleCancel}
        />
      </div>

      {/* Features Highlight */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Sparkles className="h-8 w-8 text-blue-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Smart Presets</h3>
              <p className="text-sm text-gray-600">
                AI-powered template suggestions based on procurement stage and type
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-green-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Dynamic Approvals</h3>
              <p className="text-sm text-gray-600">
                Automatically configure approval workflows based on procurement requirements
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 text-purple-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Live Preview</h3>
              <p className="text-sm text-gray-600">
                See exactly how your template will look with real-time preview
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
