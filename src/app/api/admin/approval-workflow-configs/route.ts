import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { getCurrentUser } from '@/lib/auth';
import { prisma } from '@/lib/db';

// Validation schemas
const createWorkflowConfigSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  entityType: z.enum(['procurement', 'vendor', 'contract', 'payment', 'purchase_order', 'invoice']),
  stage: z.string().optional(),
  config: z.object({
    approvalLevels: z.array(z.object({
      level: z.number(),
      name: z.string(),
      description: z.string().optional(),
      approvers: z.array(z.object({
        type: z.enum(['USER', 'ROLE', 'DEPARTMENT']),
        id: z.string(),
        name: z.string(),
        required: z.boolean().default(true),
      })),
      conditions: z.array(z.object({
        field: z.string(),
        operator: z.string(),
        value: z.any(),
      })).optional(),
      timeoutHours: z.number().optional(),
      escalation: z.object({
        enabled: z.boolean().default(false),
        escalateToIds: z.array(z.string()).optional(),
        escalateAfterHours: z.number().optional(),
      }).optional(),
    })),
    globalSettings: z.object({
      allowDelegation: z.boolean().default(false),
      requireComments: z.boolean().default(false),
      notifyOnStart: z.boolean().default(true),
      notifyOnComplete: z.boolean().default(true),
      autoReminders: z.object({
        enabled: z.boolean().default(true),
        intervalHours: z.number().default(24),
        maxReminders: z.number().default(3),
      }),
    }),
    valueThresholds: z.array(z.object({
      minValue: z.number(),
      maxValue: z.number().optional(),
      requiredApprovers: z.array(z.string()),
      additionalSteps: z.array(z.string()).optional(),
    })).optional(),
  }),
});

const updateWorkflowConfigSchema = createWorkflowConfigSchema.partial().extend({
  id: z.string(),
});

// GET /api/admin/approval-workflow-configs
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const entityType = searchParams.get('entityType');
    const stage = searchParams.get('stage');
    const isActive = searchParams.get('isActive');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (entityType) where.entityType = entityType;
    if (stage) where.stage = stage;
    if (isActive !== null) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [configs, total] = await Promise.all([
      prisma.approvalWorkflowConfig.findMany({
        where,
        orderBy: [
          { entityType: 'asc' },
          { stage: 'asc' },
          { createdAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.approvalWorkflowConfig.count({ where }),
    ]);

    return NextResponse.json({
      configs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching workflow configs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workflow configs' },
      { status: 500 }
    );
  }
}

// POST /api/admin/approval-workflow-configs
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createWorkflowConfigSchema.parse(body);

    // Validate approval levels sequence
    const levels = validatedData.config.approvalLevels;
    const sequences = levels.map(level => level.level);
    const uniqueSequences = [...new Set(sequences)];
    
    if (sequences.length !== uniqueSequences.length) {
      return NextResponse.json(
        { error: 'Approval levels must have unique level numbers' },
        { status: 400 }
      );
    }

    // Validate that level numbers are sequential starting from 1
    const sortedLevels = [...sequences].sort((a, b) => a - b);
    for (let i = 0; i < sortedLevels.length; i++) {
      if (sortedLevels[i] !== i + 1) {
        return NextResponse.json(
          { error: 'Approval levels must be sequential starting from 1' },
          { status: 400 }
        );
      }
    }

    const config = await prisma.approvalWorkflowConfig.create({
      data: validatedData,
    });

    return NextResponse.json(config, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating workflow config:', error);
    return NextResponse.json(
      { error: 'Failed to create workflow config' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/approval-workflow-configs - Update workflow config
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = updateWorkflowConfigSchema.parse(body);

    // Update workflow config by ID
    const { id, ...updateData } = validatedData;
    if (!id) {
      return NextResponse.json({ error: 'Workflow config ID is required' }, { status: 400 });
    }

    const updatedConfig = await prisma.approvalWorkflowConfig.update({
      where: { id },
      data: {
        ...updateData,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedConfig,
    });

  } catch (error) {
    console.error('Error updating workflow config:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow config' },
      { status: 500 }
    );
  }
}
