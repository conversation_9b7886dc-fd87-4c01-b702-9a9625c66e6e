import { NextRequest } from "next/server";
import { z } from "zod";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createSuccessResponse, handleApiError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

const createProcurementContentSchema = z.object({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  excerpt: z.string().optional(),
  type: z.enum(["GUIDELINE", "ANNOUNCEMENT", "PROCEDURE", "FAQ", "TERMS"]),
  status: z.enum(["DRAFT", "PUBLISHED"]),
  featuredImage: z.string().optional(),
});

// GET /api/admin/procurement-content - List all procurement content
const getHandler = requireRoles(["ADMIN"])(async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return createSuccessResponse({ error: "Unauthorized" }, "Unauthorized", 401);
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get("type");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");

    const where: any = {};
    if (type) where.type = type;

    const [content, totalCount] = await Promise.all([
      prisma.procurementContent.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.procurementContent.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return createSuccessResponse({
      content,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching procurement content:", error);
    return handleApiError(error);
  }
});

// POST /api/admin/procurement-content - Create new procurement content
const postHandler = requireRoles(["ADMIN"])(async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    const body = await request.json();

    const validatedData = createProcurementContentSchema.parse(body);

    // Generate slug from title
    const slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();

    // Ensure slug is unique
    let finalSlug = slug;
    let counter = 1;
    while (await prisma.procurementContent.findUnique({ where: { slug: finalSlug } })) {
      finalSlug = `${slug}-${counter}`;
      counter++;
    }

    const contentData: any = {
      title: validatedData.title,
      slug: finalSlug,
      content: validatedData.content,
      excerpt: validatedData.excerpt,
      type: validatedData.type,
      status: validatedData.status,
      featuredImage: validatedData.featuredImage,
      authorId: user.id,
    };

    if (validatedData.status === "PUBLISHED") {
      contentData.publishedAt = new Date();
    }

    const procurementContent = await prisma.procurementContent.create({
      data: contentData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return createSuccessResponse(procurementContent);
  } catch (error) {
    console.error("Error creating procurement content:", error);
    return handleApiError(error);
  }
});

export const GET = getHandler;
export const POST = postHandler;
