import { NextRequest } from "next/server";
import { z } from "zod";

import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse } from "@/lib/errors";
import { adminOnly } from "@/lib/security/rbac-middleware";

const createPermissionSchema = z.object({
  name: z.string().min(1, "Permission name is required"),
  description: z.string().min(1, "Description is required"),
  resource: z.string().min(1, "Resource is required"),
  action: z.string().min(1, "Action is required"),
  conditions: z.record(z.unknown()).optional(),
});

async function handleGET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const resource = searchParams.get("resource");
    const action = searchParams.get("action");

    const where: { resource?: string; action?: string } = {};
    if (resource) where.resource = resource;
    if (action) where.action = action;

    // Fetch permissions from database
    const permissions = await prisma.permission.findMany({
      where,
      orderBy: { resource: 'asc' }
    });

    // If no permissions exist, seed with predefined permissions
    if (permissions.length === 0) {
      const predefinedPermissions = [
        { resource: 'procurement', action: 'create', description: 'Create procurement' },
        { resource: 'procurement', action: 'read', description: 'View procurement' },
        { resource: 'procurement', action: 'update', description: 'Update procurement' },
        { resource: 'procurement', action: 'delete', description: 'Delete procurement' },
        { resource: 'vendor', action: 'create', description: 'Create vendor' },
        { resource: 'vendor', action: 'read', description: 'View vendor' },
        { resource: 'vendor', action: 'update', description: 'Update vendor' },
        { resource: 'vendor', action: 'approve', description: 'Approve vendor' },
        { resource: 'approval', action: 'create', description: 'Create approval' },
        { resource: 'approval', action: 'approve', description: 'Approve items' },
        { resource: 'document', action: 'create', description: 'Create document' },
        { resource: 'document', action: 'read', description: 'View document' },
        { resource: 'admin', action: 'manage', description: 'Admin management' },
      ];

      // Seed permissions
      await prisma.permission.createMany({
        data: predefinedPermissions,
        skipDuplicates: true
      });

      // Fetch the newly created permissions
      const seededPermissions = await prisma.permission.findMany({
        where,
        orderBy: { resource: 'asc' }
      });

      let filteredPermissions = seededPermissions;
      if (resource) {
        filteredPermissions = filteredPermissions.filter(p => p.resource === resource);
      }
      if (action) {
        filteredPermissions = filteredPermissions.filter(p => p.action === action);
      }

      return createSuccessResponse(filteredPermissions);
    } else {
      // Filter existing permissions
      let filteredPermissions = permissions;
      if (resource) {
        filteredPermissions = filteredPermissions.filter(p => p.resource === resource);
      }
      if (action) {
        filteredPermissions = filteredPermissions.filter(p => p.action === action);
      }

      return createSuccessResponse(filteredPermissions);
    }
  } catch (error) {
    return handleApiError(error);
  }
}

async function handlePOST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createPermissionSchema.parse(body);

    // Create new permission in database
    const permission = await prisma.permission.create({
      data: validatedData
    });

    return createSuccessResponse(
      permission,
      "Permission created successfully",
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}

export const GET = adminOnly()(handleGET);
export const POST = adminOnly()(handlePOST);
