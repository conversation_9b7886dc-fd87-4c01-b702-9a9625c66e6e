import { NextRequest, NextResponse } from "next/server";

import { auditRead } from "@/lib/audit/audit-middleware";
import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { handleApiError, createSuccessResponse, NotFoundError } from "@/lib/errors";
import { requireRoles } from "@/lib/security/rbac-middleware";

// Get vendor verification steps
const getHandler = requireRoles(["ADMIN", "APPROVER"])(
  auditRead("vendor_verification_steps")(async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    try {
      const user = await getCurrentUser(request);
      if (!user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const vendorId = params.id;

      // Get vendor to ensure it exists
      const vendor = await prisma.vendor.findUnique({
        where: { id: vendorId },
        select: { id: true, companyName: true, status: true },
      });

      if (!vendor) {
        throw new NotFoundError("Vendor not found");
      }

      // Get all verification steps for this vendor
      const verificationSteps = await prisma.vendorVerificationStep.findMany({
        where: { vendorId },
        include: {
          verifiedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { verifiedAt: "desc" },
      });

      return createSuccessResponse(verificationSteps);
    } catch (error) {
      return handleApiError(error);
    }
  })
);

export { getHandler as GET };