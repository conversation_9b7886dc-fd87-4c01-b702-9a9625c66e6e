import { NextRequest, NextResponse } from 'next/server';

import { getCurrentUser } from '@/lib/auth';
import { documentService } from '@/lib/documents/document-service';
import { templateEngine } from '@/lib/documents/template-engine';

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);

    const body = await request.json();
    const {
      templateId,
      name,
      data,
      entityType,
      entityId,
      generatePdf,
      autoSave,
      validateData,
    } = body;

    if (!templateId || !name || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: templateId, name, data' },
        { status: 400 }
      );
    }

    // Validate template and data before generation
    if (validateData !== false) {
      try {
        await templateEngine.validateTemplateData(templateId, data);
      } catch (validationError) {
        return NextResponse.json(
          {
            error: 'Template data validation failed',
            details: validationError instanceof Error ? validationError.message : 'Validation failed'
          },
          { status: 400 }
        );
      }
    }

    // Generate document from template
    const document = await documentService.generateDocument(user.id, {
      templateId,
      name,
      data,
      entityType,
      entityId,
      generatePdf: generatePdf !== false,
      autoSave: autoSave === true,
      validateData: validateData !== false,
    });

    return NextResponse.json({
      success: true,
      document,
    });
  } catch (error) {
    console.error('Error generating document:', error);
    
    if (error instanceof Error && error.message.includes('Missing required variables')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes('Invalid data type')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    );
  }
}
