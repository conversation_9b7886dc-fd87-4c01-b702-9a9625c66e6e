'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Save,
  Eye,
  Type,
  Image,
  Square,
  Settings,
  Trash2,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  Grid,
  Layers,
  PenTool,
} from 'lucide-react';
import React, { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { mockApiService } from '@/lib/mock-data/workflow-data';

// Types
interface DocumentElement {
  id: string;
  type: 'text' | 'image' | 'signature' | 'table' | 'variable' | 'shape';
  position: { x: number; y: number };
  size: { width: number; height: number };
  properties: {
    content?: string;
    fontSize?: number;
    fontFamily?: string;
    fontWeight?: string;
    color?: string;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    textAlign?: 'left' | 'center' | 'right';
    variableName?: string;
    variableType?: string;
    src?: string;
    alt?: string;
    shapeType?: 'rectangle' | 'circle' | 'line';
    rows?: number;
    columns?: number;
  };
  zIndex: number;
}

interface DocumentTemplate {
  id?: string;
  name: string;
  description?: string;
  type: string;
  category: string;
  pageSize: 'A4' | 'A3' | 'Letter' | 'Legal';
  orientation: 'portrait' | 'landscape';
  margins: { top: number; right: number; bottom: number; left: number };
  elements: DocumentElement[];
  variables: Array<{
    name: string;
    type: string;
    description?: string;
    required: boolean;
    defaultValue?: unknown;
  }>;
  cssStyles?: string;
}

// API Functions
async function saveDocumentTemplate(template: DocumentTemplate): Promise<DocumentTemplate> {
  try {
    const token = localStorage.getItem("auth-token");
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const templateData = {
      name: template.name,
      description: template.description,
      type: template.type,
      category: template.category,
      content: {
        htmlTemplate: generateHTMLFromElements(template.elements, template.pageSize, template.orientation, template.margins),
        cssStyles: template.cssStyles || generateCSSFromElements(template.elements),
        signatureFields: template.elements
          .filter(el => el.type === 'signature')
          .map(el => ({
            id: el.id,
            name: el.properties.content || 'Signature',
            position: el.position,
            size: el.size,
            required: true,
          })),
      },
      variables: template.variables,
    };

    const url = template.id ? `/api/templates/${template.id}` : '/api/templates';
    const method = template.id ? 'PUT' : 'POST';

    const response = await fetch(url, {
      method,
      headers,
      body: JSON.stringify(templateData),
    });

    if (!response.ok) throw new Error('Failed to save document template');

    const result = await response.json();
    return result.data || result;
  } catch (error) {
    console.warn('API failed, using mock data:', error);
    return mockApiService.createDocumentTemplate(template);
  }
}

// Helper functions
function generateHTMLFromElements(
  elements: DocumentElement[],
  pageSize: string,
  orientation: string,
  margins: { top: number; right: number; bottom: number; left: number }
): string {
  const sortedElements = [...elements].sort((a, b) => a.zIndex - b.zIndex);

  const elementsHTML = sortedElements.map(element => {
    const style = `
      position: absolute;
      left: ${element.position.x}px;
      top: ${element.position.y}px;
      width: ${element.size.width}px;
      height: ${element.size.height}px;
      z-index: ${element.zIndex};
      ${element.properties.fontSize ? `font-size: ${element.properties.fontSize}px;` : ''}
      ${element.properties.fontFamily ? `font-family: ${element.properties.fontFamily};` : ''}
      ${element.properties.fontWeight ? `font-weight: ${element.properties.fontWeight};` : ''}
      ${element.properties.color ? `color: ${element.properties.color};` : ''}
      ${element.properties.backgroundColor ? `background-color: ${element.properties.backgroundColor};` : ''}
      ${element.properties.borderColor ? `border: ${element.properties.borderWidth || 1}px solid ${element.properties.borderColor};` : ''}
      ${element.properties.textAlign ? `text-align: ${element.properties.textAlign};` : ''}
    `;

    switch (element.type) {
      case 'text':
        return `<div style="${style}">${element.properties.content || ''}</div>`;
      case 'variable':
        return `<div style="${style}">{{${element.properties.variableName || 'variable'}}}</div>`;
      case 'image':
        return `<img src="${element.properties.src || ''}" alt="${element.properties.alt || ''}" style="${style}" />`;
      case 'signature':
        return `<div style="${style}" class="signature-field" data-signature-name="${element.properties.content || 'Signature'}">
          [Signature: ${element.properties.content || 'Signature'}]
        </div>`;
      case 'shape':
        if (element.properties.shapeType === 'rectangle') {
          return `<div style="${style}"></div>`;
        } else if (element.properties.shapeType === 'circle') {
          return `<div style="${style} border-radius: 50%;"></div>`;
        }
        return `<div style="${style}"></div>`;
      case 'table':
        const rows = element.properties.rows || 2;
        const columns = element.properties.columns || 2;
        const tableHTML = Array.from({ length: rows }, (_, rowIndex) =>
          `<tr>${Array.from({ length: columns }, (_, colIndex) =>
            `<td style="border: 1px solid #ccc; padding: 8px;">Cell ${rowIndex + 1}-${colIndex + 1}</td>`
          ).join('')}</tr>`
        ).join('');
        return `<table style="${style} border-collapse: collapse;">${tableHTML}</table>`;
      default:
        return `<div style="${style}">${element.properties.content || ''}</div>`;
    }
  }).join('\n');

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        @page {
          size: ${pageSize} ${orientation};
          margin: ${margins.top}mm ${margins.right}mm ${margins.bottom}mm ${margins.left}mm;
        }
        body {
          margin: 0;
          padding: 0;
          position: relative;
          font-family: Arial, sans-serif;
        }
        .signature-field {
          border: 2px dashed #ccc;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #666;
        }
      </style>
    </head>
    <body>
      ${elementsHTML}
    </body>
    </html>
  `;
}

function generateCSSFromElements(_elements: DocumentElement[]): string {
  return `
    .document-template {
      position: relative;
      background: white;
    }
    
    .signature-field {
      border: 2px dashed #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      background-color: #f9f9f9;
    }
    
    .signature-field:hover {
      border-color: #007bff;
      background-color: #e3f2fd;
    }
  `;
}

// Template Types and Categories
const TEMPLATE_TYPES = [
  { value: 'PURCHASE_ORDER', label: 'Purchase Order' },
  { value: 'BAST', label: 'BAST (Berita Acara Serah Terima)' },
  { value: 'CONTRACT', label: 'Contract' },
  { value: 'INVOICE', label: 'Invoice' },
  { value: 'RFQ', label: 'Request for Quotation' },
  { value: 'AANWIJZING', label: 'Aanwijzing' },
  { value: 'EVALUATION_REPORT', label: 'Evaluation Report' },
  { value: 'AWARD_LETTER', label: 'Award Letter' },
  { value: 'PURCHASE_REQUISITION', label: 'Purchase Requisition' },
  { value: 'DELIVERY_NOTE', label: 'Delivery Note' },
  { value: 'CUSTOM', label: 'Custom Document' },
];

const TEMPLATE_CATEGORIES = [
  { value: 'PROCUREMENT', label: 'Procurement' },
  { value: 'FINANCIAL', label: 'Financial' },
  { value: 'LEGAL', label: 'Legal' },
  { value: 'ADMINISTRATIVE', label: 'Administrative' },
  { value: 'TECHNICAL', label: 'Technical' },
];

const PAGE_SIZES = [
  { value: 'A4', label: 'A4 (210 × 297 mm)' },
  { value: 'A3', label: 'A3 (297 × 420 mm)' },
  { value: 'Letter', label: 'Letter (8.5 × 11 in)' },
  { value: 'Legal', label: 'Legal (8.5 × 14 in)' },
];

export function DocumentBuilder() {
  const [template, setTemplate] = useState<DocumentTemplate>({
    name: '',
    description: '',
    type: 'PURCHASE_ORDER',
    category: 'PROCUREMENT',
    pageSize: 'A4',
    orientation: 'portrait',
    margins: { top: 20, right: 20, bottom: 20, left: 20 },
    elements: [],
    variables: [],
  });

  const [selectedElement, setSelectedElement] = useState<DocumentElement | null>(null);

  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [showGrid, setShowGrid] = useState(true);

  const [nextZIndex, setNextZIndex] = useState(1);

  const canvasRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();

  const saveMutation = useMutation({
    mutationFn: saveDocumentTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-templates'] });
      toast.success('Document template saved successfully');
      setIsSaveDialogOpen(false);
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to save document template');
    },
  });

  const addElement = useCallback((type: DocumentElement['type']) => {
    const newElement: DocumentElement = {
      id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      position: { x: 50, y: 50 },
      size: { width: 200, height: 50 },
      properties: {
        content: type === 'text' ? 'Sample Text' :
          type === 'signature' ? 'Signature Field' :
            type === 'variable' ? 'Variable' : '',
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#000000',
        ...(type === 'table' && { rows: 3, columns: 3 }),
        ...(type === 'shape' && { shapeType: 'rectangle', borderColor: '#000000', borderWidth: 1 }),
        ...(type === 'variable' && { variableName: 'sampleVariable', variableType: 'text' }),
      },
      zIndex: nextZIndex,
    };

    setTemplate(prev => ({
      ...prev,
      elements: [...prev.elements, newElement],
    }));
    setNextZIndex(prev => prev + 1);
    setSelectedElement(newElement);
  }, [nextZIndex]);

  const updateElement = useCallback((elementId: string, updates: Partial<DocumentElement>) => {
    setTemplate(prev => ({
      ...prev,
      elements: prev.elements.map(el =>
        el.id === elementId ? { ...el, ...updates } : el
      ),
    }));

    if (selectedElement?.id === elementId) {
      setSelectedElement(prev => prev ? { ...prev, ...updates } : null);
    }
  }, [selectedElement]);

  const deleteElement = useCallback((elementId: string) => {
    setTemplate(prev => ({
      ...prev,
      elements: prev.elements.filter(el => el.id !== elementId),
    }));

    if (selectedElement?.id === elementId) {
      setSelectedElement(null);
    }
  }, [selectedElement]);

  const handleSave = useCallback(() => {
    if (!template.name.trim()) {
      toast.error('Please enter a template name');
      return;
    }
    saveMutation.mutate(template);
  }, [template, saveMutation]);

  const getCanvasSize = () => {
    const sizes = {
      A4: template.orientation === 'portrait' ? { width: 595, height: 842 } : { width: 842, height: 595 },
      A3: template.orientation === 'portrait' ? { width: 842, height: 1191 } : { width: 1191, height: 842 },
      Letter: template.orientation === 'portrait' ? { width: 612, height: 792 } : { width: 792, height: 612 },
      Legal: template.orientation === 'portrait' ? { width: 612, height: 1008 } : { width: 1008, height: 612 },
    };
    return sizes[template.pageSize];
  };

  const canvasSize = getCanvasSize();
  const scaledCanvasSize = {
    width: (canvasSize.width * zoom) / 100,
    height: (canvasSize.height * zoom) / 100,
  };

  return (
    <div className="h-screen flex flex-col">
      {/* Toolbar */}
      <Card className="rounded-none border-x-0 border-t-0">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('text')}
                >
                  <Type className="h-4 w-4 mr-2" />
                  Text
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('variable')}
                >
                  <PenTool className="h-4 w-4 mr-2" />
                  Variable
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('image')}
                >
                  <Image className="h-4 w-4 mr-2" />
                  Image
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('signature')}
                >
                  <PenTool className="h-4 w-4 mr-2" />
                  Signature
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('table')}
                >
                  <Grid className="h-4 w-4 mr-2" />
                  Table
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addElement('shape')}
                >
                  <Square className="h-4 w-4 mr-2" />
                  Shape
                </Button>
              </div>

              <div className="h-6 w-px bg-gray-300" />

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Undo className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Redo className="h-4 w-4" />
                </Button>
              </div>

              <div className="h-6 w-px bg-gray-300" />

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoom(Math.max(25, zoom - 25))}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium w-12 text-center">{zoom}%</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setZoom(Math.min(200, zoom + 25))}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  variant={showGrid ? "default" : "outline"}
                  size="sm"
                  onClick={() => setShowGrid(!showGrid)}
                >
                  <Grid className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => setIsPropertiesOpen(true)}>
                <Settings className="h-4 w-4 mr-2" />
                Properties
              </Button>
              <Button variant="outline">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Save Document Template</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="template-name">Template Name</Label>
                      <Input
                        id="template-name"
                        value={template.name}
                        onChange={(e) => setTemplate(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter template name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="template-description">Description</Label>
                      <Textarea
                        id="template-description"
                        value={template.description || ''}
                        onChange={(e) => setTemplate(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Enter template description"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>Type</Label>
                        <Select
                          value={template.type}
                          onValueChange={(value) => setTemplate(prev => ({ ...prev, type: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {TEMPLATE_TYPES.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Category</Label>
                        <Select
                          value={template.category}
                          onValueChange={(value) => setTemplate(prev => ({ ...prev, category: value }))}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {TEMPLATE_CATEGORIES.map((category) => (
                              <SelectItem key={category.value} value={category.value}>
                                {category.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsSaveDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSave} disabled={saveMutation.isPending}>
                        {saveMutation.isPending ? 'Saving...' : 'Save Template'}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Area */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <Card className="w-80 rounded-none border-y-0 border-l-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Layers & Properties
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Page Settings */}
            <div className="space-y-3">
              <Label className="text-sm font-semibold">Page Settings</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Size</Label>
                  <Select
                    value={template.pageSize}
                    onValueChange={(value: string) => setTemplate(prev => ({ ...prev, pageSize: value as 'A4' | 'A3' | 'Letter' | 'Legal' }))}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {PAGE_SIZES.map((size) => (
                        <SelectItem key={size.value} value={size.value}>
                          {size.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Orientation</Label>
                  <Select
                    value={template.orientation}
                    onValueChange={(value: string) => setTemplate(prev => ({ ...prev, orientation: value as 'portrait' | 'landscape' }))}
                  >
                    <SelectTrigger className="h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="portrait">Portrait</SelectItem>
                      <SelectItem value="landscape">Landscape</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Elements List */}
            <div className="space-y-3">
              <Label className="text-sm font-semibold">Elements ({template.elements.length})</Label>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {template.elements.map((element) => (
                  <div
                    key={element.id}
                    className={`p-2 border rounded cursor-pointer hover:bg-gray-50 ${selectedElement?.id === element.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                      }`}
                    onClick={() => setSelectedElement(element)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {element.type === 'text' && <Type className="h-4 w-4" />}
                        {element.type === 'image' && <Image className="h-4 w-4" />}
                        {element.type === 'signature' && <PenTool className="h-4 w-4" />}
                        {element.type === 'table' && <Grid className="h-4 w-4" />}
                        {element.type === 'variable' && <PenTool className="h-4 w-4" />}
                        {element.type === 'shape' && <Square className="h-4 w-4" />}
                        <span className="text-sm font-medium capitalize">{element.type}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteElement(element.id);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {element.properties.content || element.properties.variableName || 'Untitled'}
                    </div>
                  </div>
                ))}
                {template.elements.length === 0 && (
                  <div className="text-center py-8 text-gray-500 text-sm">
                    No elements added yet. Use the toolbar to add elements.
                  </div>
                )}
              </div>
            </div>

            {/* Element Properties */}
            {selectedElement && (
              <div className="space-y-3 border-t pt-3">
                <Label className="text-sm font-semibold">Element Properties</Label>
                <div className="space-y-2">
                  {selectedElement.type === 'text' && (
                    <>
                      <div>
                        <Label className="text-xs">Content</Label>
                        <Textarea
                          value={selectedElement.properties.content || ''}
                          onChange={(e) => updateElement(selectedElement.id, {
                            properties: { ...selectedElement.properties, content: e.target.value }
                          })}
                          className="h-20"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">Font Size</Label>
                          <Input
                            type="number"
                            value={selectedElement.properties.fontSize || 14}
                            onChange={(e) => updateElement(selectedElement.id, {
                              properties: { ...selectedElement.properties, fontSize: parseInt(e.target.value) }
                            })}
                            className="h-8"
                          />
                        </div>
                        <div>
                          <Label className="text-xs">Color</Label>
                          <Input
                            type="color"
                            value={selectedElement.properties.color || '#000000'}
                            onChange={(e) => updateElement(selectedElement.id, {
                              properties: { ...selectedElement.properties, color: e.target.value }
                            })}
                            className="h-8"
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {selectedElement.type === 'variable' && (
                    <>
                      <div>
                        <Label className="text-xs">Variable Name</Label>
                        <Input
                          value={selectedElement.properties.variableName || ''}
                          onChange={(e) => updateElement(selectedElement.id, {
                            properties: { ...selectedElement.properties, variableName: e.target.value }
                          })}
                          className="h-8"
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Variable Type</Label>
                        <Select
                          value={selectedElement.properties.variableType || 'text'}
                          onValueChange={(value) => updateElement(selectedElement.id, {
                            properties: { ...selectedElement.properties, variableType: value }
                          })}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">Text</SelectItem>
                            <SelectItem value="number">Number</SelectItem>
                            <SelectItem value="date">Date</SelectItem>
                            <SelectItem value="currency">Currency</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}

                  {selectedElement.type === 'signature' && (
                    <div>
                      <Label className="text-xs">Signature Label</Label>
                      <Input
                        value={selectedElement.properties.content || ''}
                        onChange={(e) => updateElement(selectedElement.id, {
                          properties: { ...selectedElement.properties, content: e.target.value }
                        })}
                        className="h-8"
                      />
                    </div>
                  )}

                  {selectedElement.type === 'table' && (
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label className="text-xs">Rows</Label>
                        <Input
                          type="number"
                          value={selectedElement.properties.rows || 2}
                          onChange={(e) => updateElement(selectedElement.id, {
                            properties: { ...selectedElement.properties, rows: parseInt(e.target.value) }
                          })}
                          className="h-8"
                        />
                      </div>
                      <div>
                        <Label className="text-xs">Columns</Label>
                        <Input
                          type="number"
                          value={selectedElement.properties.columns || 2}
                          onChange={(e) => updateElement(selectedElement.id, {
                            properties: { ...selectedElement.properties, columns: parseInt(e.target.value) }
                          })}
                          className="h-8"
                        />
                      </div>
                    </div>
                  )}

                  {/* Position and Size */}
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs">X Position</Label>
                      <Input
                        type="number"
                        value={selectedElement.position.x}
                        onChange={(e) => updateElement(selectedElement.id, {
                          position: { ...selectedElement.position, x: parseInt(e.target.value) }
                        })}
                        className="h-8"
                      />
                    </div>
                    <div>
                      <Label className="text-xs">Y Position</Label>
                      <Input
                        type="number"
                        value={selectedElement.position.y}
                        onChange={(e) => updateElement(selectedElement.id, {
                          position: { ...selectedElement.position, y: parseInt(e.target.value) }
                        })}
                        className="h-8"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs">Width</Label>
                      <Input
                        type="number"
                        value={selectedElement.size.width}
                        onChange={(e) => updateElement(selectedElement.id, {
                          size: { ...selectedElement.size, width: parseInt(e.target.value) }
                        })}
                        className="h-8"
                      />
                    </div>
                    <div>
                      <Label className="text-xs">Height</Label>
                      <Input
                        type="number"
                        value={selectedElement.size.height}
                        onChange={(e) => updateElement(selectedElement.id, {
                          size: { ...selectedElement.size, height: parseInt(e.target.value) }
                        })}
                        className="h-8"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Canvas Area */}
        <div className="flex-1 bg-gray-100 overflow-auto p-8">
          <div className="flex justify-center">
            <div
              ref={canvasRef}
              className="relative bg-white shadow-lg"
              style={{
                width: scaledCanvasSize.width,
                height: scaledCanvasSize.height,
                backgroundImage: showGrid ?
                  'radial-gradient(circle, #ccc 1px, transparent 1px)' : 'none',
                backgroundSize: showGrid ? '20px 20px' : 'auto',
              }}
            >
              {/* Render Elements */}
              {template.elements.map((element) => {
                const scaledPosition = {
                  x: (element.position.x * zoom) / 100,
                  y: (element.position.y * zoom) / 100,
                };
                const scaledSize = {
                  width: (element.size.width * zoom) / 100,
                  height: (element.size.height * zoom) / 100,
                };

                return (
                  <div
                    key={element.id}
                    className={`absolute border-2 cursor-move ${selectedElement?.id === element.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-transparent hover:border-gray-300'
                      }`}
                    style={{
                      left: scaledPosition.x,
                      top: scaledPosition.y,
                      width: scaledSize.width,
                      height: scaledSize.height,
                      zIndex: element.zIndex,
                      fontSize: ((element.properties.fontSize || 14) * zoom) / 100,
                      fontFamily: element.properties.fontFamily || 'Arial',
                      fontWeight: element.properties.fontWeight || 'normal',
                      color: element.properties.color || '#000000',
                      backgroundColor: element.properties.backgroundColor || 'transparent',
                      textAlign: element.properties.textAlign || 'left',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: element.properties.textAlign === 'center' ? 'center' :
                        element.properties.textAlign === 'right' ? 'flex-end' : 'flex-start',
                      padding: '4px',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedElement(element);
                    }}
                    onMouseDown={(e) => {
                      // Basic drag functionality placeholder
                      e.preventDefault();
                    }}
                  >
                    {/* Element Content */}
                    {element.type === 'text' && (
                      <div className="w-full h-full overflow-hidden">
                        {element.properties.content || 'Text Element'}
                      </div>
                    )}

                    {element.type === 'variable' && (
                      <div className="w-full h-full overflow-hidden bg-yellow-100 border border-yellow-300 rounded">
                        {`{{${element.properties.variableName || 'variable'}}}`}
                      </div>
                    )}

                    {element.type === 'signature' && (
                      <div className="w-full h-full border-2 border-dashed border-gray-400 bg-gray-50 flex items-center justify-center text-gray-600">
                        [Signature: {element.properties.content || 'Signature'}]
                      </div>
                    )}

                    {element.type === 'image' && (
                      <div className="w-full h-full border border-gray-300 bg-gray-100 flex items-center justify-center text-gray-500">
                        <Image className="h-8 w-8" />
                        <span className="ml-2">Image</span>
                      </div>
                    )}

                    {element.type === 'table' && (
                      <div className="w-full h-full border border-gray-300">
                        <table className="w-full h-full border-collapse">
                          <tbody>
                            {Array.from({ length: element.properties.rows || 2 }, (_, rowIndex) => (
                              <tr key={rowIndex}>
                                {Array.from({ length: element.properties.columns || 2 }, (_, colIndex) => (
                                  <td
                                    key={colIndex}
                                    className="border border-gray-300 p-1 text-xs"
                                  >
                                    Cell {rowIndex + 1}-{colIndex + 1}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}

                    {element.type === 'shape' && (
                      <div
                        className="w-full h-full"
                        style={{
                          backgroundColor: element.properties.backgroundColor || 'transparent',
                          border: `${element.properties.borderWidth || 1}px solid ${element.properties.borderColor || '#000000'}`,
                          borderRadius: element.properties.shapeType === 'circle' ? '50%' : '0',
                        }}
                      />
                    )}

                    {/* Selection Handles */}
                    {selectedElement?.id === element.id && (
                      <>
                        <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 border border-white rounded-full cursor-nw-resize" />
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 border border-white rounded-full cursor-ne-resize" />
                        <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 border border-white rounded-full cursor-sw-resize" />
                        <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 border border-white rounded-full cursor-se-resize" />
                      </>
                    )}
                  </div>
                );
              })}

              {/* Canvas Click Handler */}
              <div
                className="absolute inset-0 -z-10"
                onClick={() => setSelectedElement(null)}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
