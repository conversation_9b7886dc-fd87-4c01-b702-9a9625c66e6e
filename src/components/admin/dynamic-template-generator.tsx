'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Wand2,
  FileText,
  Settings,
  Eye,
  Save,
  Download,
  Plus,
  Trash2,
  Copy,
  Move,
  Type,
  Image,
  Table,
  List,
  FileSignature,
  Layout,
  Palette,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

interface TemplateGeneratorProps {
  procurementId?: string;
  stageType?: string;
  approvalWorkflowId?: string;
  onSave?: (template: any) => void;
  onCancel?: () => void;
}

interface TemplateComponent {
  id: string;
  type: 'text' | 'table' | 'list' | 'image' | 'signature' | 'approval' | 'variable' | 'container';
  content: string;
  style?: Record<string, any>;
  properties?: Record<string, any>;
  conditions?: Array<{
    field: string;
    operator: string;
    value: any;
  }>;
  children?: TemplateComponent[];
  src?: string;
  alt?: string;
  items?: string[];
  headers?: string[];
  rows?: string[] | string;
}

interface ApprovalStep {
  id: string;
  name: string;
  approvers: Array<{
    id: string;
    name: string;
    role: string;
  }>;
  sequence: number;
  isRequired: boolean;
}

const PROCUREMENT_STAGES = [
  { value: 'announcement', label: 'Announcement', icon: '📢' },
  { value: 'submission', label: 'Submission', icon: '📝' },
  { value: 'evaluation', label: 'Evaluation', icon: '📊' },
  { value: 'award', label: 'Award', icon: '🏆' },
  { value: 'contract', label: 'Contract', icon: '📋' }
];

const COMPONENT_TYPES = [
  { type: 'text', icon: Type, label: 'Text Block', description: 'Add formatted text content' },
  { type: 'variable', icon: FileText, label: 'Dynamic Variable', description: 'Insert procurement data' },
  { type: 'table', icon: Table, label: 'Data Table', description: 'Structured data display' },
  { type: 'list', icon: List, label: 'List Items', description: 'Bulleted or numbered lists' },
  { type: 'image', icon: Image, label: 'Image/Logo', description: 'Company logos and images' },
  { type: 'signature', icon: FileSignature, label: 'Signature Field', description: 'Digital signature area' },
  { type: 'approval', icon: Users, label: 'Approval Section', description: 'Approval workflow display' }
];

const TEMPLATE_PRESETS = {
  announcement: {
    name: 'Procurement Announcement',
    components: [
      { type: 'text', content: 'PROCUREMENT ANNOUNCEMENT', style: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' } },
      { type: 'variable', content: '{{procurement.title}}', style: { fontSize: 16, marginBottom: 10 } },
      { type: 'text', content: 'Submission Deadline:', style: { fontWeight: 'bold' } },
      { type: 'variable', content: '{{procurement.submissionDeadline}}', style: { color: '#d32f2f' } }
    ]
  },
  evaluation: {
    name: 'Evaluation Report',
    components: [
      { type: 'text', content: 'VENDOR EVALUATION REPORT', style: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' } },
      { type: 'table', content: 'evaluation_scores', properties: { headers: ['Vendor', 'Technical', 'Commercial', 'Total'] } },
      { type: 'approval', content: 'evaluation_approval', properties: { workflow: 'evaluation' } }
    ]
  },
  contract: {
    name: 'Contract Agreement',
    components: [
      { type: 'text', content: 'CONTRACT AGREEMENT', style: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' } },
      { type: 'variable', content: '{{contract.parties}}', style: { marginBottom: 15 } },
      { type: 'text', content: 'Terms and Conditions:', style: { fontSize: 14, fontWeight: 'bold' } },
      { type: 'approval', content: 'contract_approval', properties: { workflow: 'contract' } }
    ]
  }
};

export function DynamicTemplateGenerator({
  procurementId,
  stageType = 'announcement',
  approvalWorkflowId,
  onSave,
  onCancel
}: TemplateGeneratorProps) {
  const [activeTab, setActiveTab] = useState<'wizard' | 'design' | 'approval' | 'preview'>('wizard');
  const [selectedStage, setSelectedStage] = useState(stageType);
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [components, setComponents] = useState<TemplateComponent[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [approvalSteps, setApprovalSteps] = useState<ApprovalStep[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Auto-generate template name based on stage
  useEffect(() => {
    const stage = PROCUREMENT_STAGES.find(s => s.value === selectedStage);
    if (stage && !templateName) {
      setTemplateName(`${stage.label} Template - ${new Date().toLocaleDateString()}`);
    }
  }, [selectedStage, templateName]);

  // Load preset components when stage changes
  const loadPresetComponents = useCallback((stage: string) => {
    const preset = TEMPLATE_PRESETS[stage as keyof typeof TEMPLATE_PRESETS];
    if (preset) {
      const newComponents = preset.components.map((comp, index) => ({
        id: `comp-${Date.now()}-${index}`,
        ...comp
      })) as TemplateComponent[];
      setComponents(newComponents);
    }
  }, []);

  const addComponent = useCallback((type: string) => {
    const newComponent: TemplateComponent = {
      id: `comp-${Date.now()}`,
      type: type as any,
      content: type === 'text' ? 'New text content' :
        type === 'variable' ? '{{variable.name}}' :
          type === 'table' ? 'table_data' :
            type === 'list' ? 'list_items' :
              type === 'approval' ? 'approval_workflow' : '',
      style: {
        fontSize: 14,
        marginBottom: 10,
        ...(type === 'text' && { fontFamily: 'Arial, sans-serif' })
      },
      properties: {}
    };

    setComponents(prev => [...prev, newComponent]);
    setSelectedComponent(newComponent.id);
  }, []);

  const updateComponent = useCallback((id: string, updates: Partial<TemplateComponent>) => {
    setComponents(prev => prev.map(comp =>
      comp.id === id ? { ...comp, ...updates } : comp
    ));
  }, []);

  const deleteComponent = useCallback((id: string) => {
    setComponents(prev => prev.filter(comp => comp.id !== id));
    if (selectedComponent === id) {
      setSelectedComponent(null);
    }
  }, [selectedComponent]);

  const generateTemplate = async () => {
    setIsGenerating(true);
    try {
      // Simulate template generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      const template = {
        name: templateName,
        description: templateDescription,
        stageType: selectedStage,
        components,
        approvalSteps,
        metadata: {
          procurementId,
          approvalWorkflowId,
          generatedAt: new Date().toISOString()
        }
      };

      onSave?.(template);
    } catch (error) {
      console.error('Error generating template:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const renderWizardTab = () => (
    <div className="space-y-6">
      {/* Stage Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5" />
            Template Wizard
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Procurement Stage</Label>
            <Select value={selectedStage} onValueChange={setSelectedStage}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {PROCUREMENT_STAGES.map(stage => (
                  <SelectItem key={stage.value} value={stage.value}>
                    <div className="flex items-center gap-2">
                      <span>{stage.icon}</span>
                      <span>{stage.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Template Name</Label>
            <Input
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="Enter template name"
            />
          </div>

          <div>
            <Label>Description</Label>
            <Textarea
              value={templateDescription}
              onChange={(e) => setTemplateDescription(e.target.value)}
              placeholder="Describe the purpose of this template"
              rows={3}
            />
          </div>

          <Button
            onClick={() => loadPresetComponents(selectedStage)}
            className="w-full"
            variant="outline"
          >
            <Wand2 className="h-4 w-4 mr-2" />
            Load Smart Preset for {PROCUREMENT_STAGES.find(s => s.value === selectedStage)?.label}
          </Button>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="outline"
              onClick={() => setActiveTab('design')}
              className="h-20 flex-col"
            >
              <Layout className="h-6 w-6 mb-2" />
              Design Template
            </Button>
            <Button
              variant="outline"
              onClick={() => setActiveTab('approval')}
              className="h-20 flex-col"
            >
              <Users className="h-6 w-6 mb-2" />
              Setup Approvals
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Dynamic Template Generator</h1>
            <p className="text-gray-600">Create intelligent document templates for procurement stages</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={generateTemplate} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Generate Template
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="wizard" className="flex items-center gap-2">
              <Wand2 className="h-4 w-4" />
              Wizard
            </TabsTrigger>
            <TabsTrigger value="design" className="flex items-center gap-2">
              <Layout className="h-4 w-4" />
              Design
            </TabsTrigger>
            <TabsTrigger value="approval" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Approvals
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="wizard" className="h-full p-6">
            {renderWizardTab()}
          </TabsContent>

          <TabsContent value="design" className="h-full">
            <div className="h-full flex">
              {/* Component Palette */}
              <div className="w-80 border-r bg-gray-50 p-4">
                <h3 className="font-semibold mb-4">Components</h3>
                <div className="space-y-2">
                  {COMPONENT_TYPES.map(({ type, icon: Icon, label, description }) => (
                    <Button
                      key={type}
                      variant="ghost"
                      onClick={() => addComponent(type)}
                      className="w-full justify-start h-auto p-3"
                    >
                      <div className="flex items-start gap-3">
                        <Icon className="h-5 w-5 mt-0.5 text-blue-600" />
                        <div className="text-left">
                          <div className="font-medium">{label}</div>
                          <div className="text-xs text-gray-500">{description}</div>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Canvas */}
              <div className="flex-1 p-6 overflow-auto">
                <div className="max-w-4xl mx-auto bg-white border rounded-lg shadow-sm min-h-[800px] p-8">
                  {components.length === 0 ? (
                    <div className="flex items-center justify-center h-64 text-gray-500">
                      <div className="text-center">
                        <Layout className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium">No components added</h3>
                        <p className="mt-1 text-sm">Start by adding components from the palette</p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {components.map((component) => (
                        <div
                          key={component.id}
                          className={`relative p-4 border rounded-md cursor-pointer transition-all ${selectedComponent === component.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                            }`}
                          onClick={() => setSelectedComponent(component.id)}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant="secondary">
                              {COMPONENT_TYPES.find(t => t.type === component.type)?.label}
                            </Badge>
                            <div className="flex items-center gap-1">
                              <Button size="sm" variant="ghost" onClick={() => deleteComponent(component.id)}>
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div style={component.style}>
                            {component.content}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Properties Panel */}
              {selectedComponent && (
                <div className="w-80 border-l bg-gray-50 p-4">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Component Properties
                  </h3>

                  {(() => {
                    const component = components.find(c => c.id === selectedComponent);
                    if (!component) return null;

                    return (
                      <div className="space-y-4">
                        {/* Content Editor */}
                        <div>
                          <Label className="text-sm">Content</Label>
                          <Textarea
                            value={component.content}
                            onChange={(e) => updateComponent(component.id, { content: e.target.value })}
                            placeholder="Enter content..."
                            rows={3}
                            className="mt-1"
                          />
                        </div>

                        {/* Style Properties */}
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Styling</Label>

                          <div className="grid grid-cols-2 gap-2">
                            <div>
                              <Label className="text-xs">Font Size</Label>
                              <Input
                                type="number"
                                value={component.style?.fontSize || 14}
                                onChange={(e) => updateComponent(component.id, {
                                  style: { ...component.style, fontSize: parseInt(e.target.value) }
                                })}
                                min="8"
                                max="72"
                                className="h-8"
                              />
                            </div>

                            <div>
                              <Label className="text-xs">Font Weight</Label>
                              <Select
                                value={component.style?.fontWeight || 'normal'}
                                onValueChange={(value) => updateComponent(component.id, {
                                  style: { ...component.style, fontWeight: value }
                                })}
                              >
                                <SelectTrigger className="h-8">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="normal">Normal</SelectItem>
                                  <SelectItem value="bold">Bold</SelectItem>
                                  <SelectItem value="lighter">Light</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div>
                            <Label className="text-xs">Text Align</Label>
                            <Select
                              value={component.style?.textAlign || 'left'}
                              onValueChange={(value) => updateComponent(component.id, {
                                style: { ...component.style, textAlign: value }
                              })}
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="left">Left</SelectItem>
                                <SelectItem value="center">Center</SelectItem>
                                <SelectItem value="right">Right</SelectItem>
                                <SelectItem value="justify">Justify</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <Label className="text-xs">Margin Bottom</Label>
                            <Input
                              type="number"
                              value={component.style?.marginBottom || 10}
                              onChange={(e) => updateComponent(component.id, {
                                style: { ...component.style, marginBottom: parseInt(e.target.value) }
                              })}
                              min="0"
                              max="100"
                              className="h-8"
                            />
                          </div>

                          <div>
                            <Label className="text-xs">Text Color</Label>
                            <Input
                              type="color"
                              value={component.style?.color || '#000000'}
                              onChange={(e) => updateComponent(component.id, {
                                style: { ...component.style, color: e.target.value }
                              })}
                              className="h-8"
                            />
                          </div>
                        </div>

                        {/* Component-specific properties */}
                        {component.type === 'variable' && (
                          <div>
                            <Label className="text-sm">Variable Name</Label>
                            <Input
                              value={component.properties?.variableName || ''}
                              onChange={(e) => updateComponent(component.id, {
                                properties: { ...component.properties, variableName: e.target.value }
                              })}
                              placeholder="e.g., procurement.title"
                              className="mt-1"
                            />
                          </div>
                        )}

                        {component.type === 'table' && (
                          <div className="space-y-2">
                            <Label className="text-sm">Table Configuration</Label>
                            <div>
                              <Label className="text-xs">Data Source</Label>
                              <Input
                                value={component.properties?.dataSource || ''}
                                onChange={(e) => updateComponent(component.id, {
                                  properties: { ...component.properties, dataSource: e.target.value }
                                })}
                                placeholder="e.g., evaluation.scores"
                                className="h-8"
                              />
                            </div>
                          </div>
                        )}

                        {component.type === 'approval' && (
                          <div>
                            <Label className="text-sm">Approval Workflow</Label>
                            <Select
                              value={component.properties?.workflowType || 'default'}
                              onValueChange={(value) => updateComponent(component.id, {
                                properties: { ...component.properties, workflowType: value }
                              })}
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="default">Default Workflow</SelectItem>
                                <SelectItem value="evaluation">Evaluation Workflow</SelectItem>
                                <SelectItem value="contract">Contract Workflow</SelectItem>
                                <SelectItem value="custom">Custom Workflow</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {/* Conditional Display */}
                        <div className="space-y-2">
                          <Label className="text-sm">Conditional Display</Label>
                          <div className="text-xs text-gray-500 mb-2">
                            Show this component only when certain conditions are met
                          </div>
                          <Button size="sm" variant="outline" className="w-full">
                            <Plus className="h-3 w-3 mr-2" />
                            Add Condition
                          </Button>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="approval" className="h-full p-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Approval Workflow Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Approval Steps</Label>
                    <Button
                      size="sm"
                      onClick={() => {
                        const newStep: ApprovalStep = {
                          id: `step-${Date.now()}`,
                          name: `Approval Step ${approvalSteps.length + 1}`,
                          approvers: [],
                          sequence: approvalSteps.length + 1,
                          isRequired: true
                        };
                        setApprovalSteps(prev => [...prev, newStep]);
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Step
                    </Button>
                  </div>

                  {approvalSteps.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Users className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium">No approval steps configured</h3>
                      <p className="mt-1 text-sm">Add approval steps to define the workflow</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {approvalSteps.map((step, index) => (
                        <Card key={step.id} className="border-l-4 border-l-blue-500">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">Step {step.sequence}</Badge>
                                <Input
                                  value={step.name}
                                  onChange={(e) => {
                                    setApprovalSteps(prev => prev.map(s =>
                                      s.id === step.id ? { ...s, name: e.target.value } : s
                                    ));
                                  }}
                                  className="w-48"
                                />
                              </div>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setApprovalSteps(prev => prev.filter(s => s.id !== step.id));
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label className="text-xs">Approvers</Label>
                                <div className="mt-1 space-y-1">
                                  {step.approvers.length === 0 ? (
                                    <p className="text-xs text-gray-500">No approvers assigned</p>
                                  ) : (
                                    step.approvers.map(approver => (
                                      <div key={approver.id} className="flex items-center gap-2 text-xs">
                                        <Badge variant="secondary">{approver.role}</Badge>
                                        <span>{approver.name}</span>
                                      </div>
                                    ))
                                  )}
                                  <Button size="sm" variant="outline" className="h-6 text-xs">
                                    <Plus className="h-3 w-3 mr-1" />
                                    Add Approver
                                  </Button>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <input
                                    type="checkbox"
                                    checked={step.isRequired}
                                    onChange={(e) => {
                                      setApprovalSteps(prev => prev.map(s =>
                                        s.id === step.id ? { ...s, isRequired: e.target.checked } : s
                                      ));
                                    }}
                                    className="rounded"
                                  />
                                  <Label className="text-xs">Required Step</Label>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Approval Preview */}
              {approvalSteps.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Workflow Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2 overflow-x-auto pb-2">
                      {approvalSteps.map((step, index) => (
                        <React.Fragment key={step.id}>
                          <div className="flex flex-col items-center min-w-0 flex-shrink-0">
                            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-xs font-medium">
                              {step.sequence}
                            </div>
                            <div className="text-xs text-center mt-1 max-w-20 truncate">
                              {step.name}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {step.approvers.length} approver{step.approvers.length !== 1 ? 's' : ''}
                            </div>
                          </div>
                          {index < approvalSteps.length - 1 && (
                            <div className="w-8 h-0.5 bg-gray-300 flex-shrink-0"></div>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="preview" className="h-full p-6">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Template Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-white border rounded-lg shadow-sm p-8 max-w-4xl mx-auto">
                    {/* Template Header */}
                    <div className="text-center border-b pb-4 mb-6">
                      <div className="w-16 h-16 bg-gray-200 rounded mx-auto mb-2"></div>
                      <h1 className="text-xl font-bold">Company Name</h1>
                      <h2 className="text-lg font-semibold mt-2">
                        {PROCUREMENT_STAGES.find(s => s.value === selectedStage)?.label} Document
                      </h2>
                    </div>

                    {/* Template Body */}
                    <div className="space-y-4">
                      {components.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          <FileText className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2">No components to preview</p>
                          <p className="text-sm">Add components in the Design tab</p>
                        </div>
                      ) : (
                        components.map((component) => (
                          <div key={component.id} className="border-l-2 border-gray-200 pl-4">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="text-xs">
                                {COMPONENT_TYPES.find(t => t.type === component.type)?.label}
                              </Badge>
                            </div>
                            <div style={component.style} className="preview-content">
                              {component.type === 'text' && (
                                <div>{component.content}</div>
                              )}
                              {component.type === 'variable' && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded px-2 py-1 inline-block">
                                  {component.content}
                                </div>
                              )}
                              {component.type === 'table' && (
                                <div className="border rounded">
                                  <table className="w-full text-sm">
                                    <thead className="bg-gray-50">
                                      <tr>
                                        <th className="p-2 text-left">Column 1</th>
                                        <th className="p-2 text-left">Column 2</th>
                                        <th className="p-2 text-left">Column 3</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr>
                                        <td className="p-2 border-t">Sample Data</td>
                                        <td className="p-2 border-t">Sample Data</td>
                                        <td className="p-2 border-t">Sample Data</td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              )}
                              {component.type === 'list' && (
                                <ul className="list-disc list-inside space-y-1">
                                  <li>Sample list item 1</li>
                                  <li>Sample list item 2</li>
                                  <li>Sample list item 3</li>
                                </ul>
                              )}
                              {component.type === 'signature' && (
                                <div className="border-2 border-dashed border-gray-300 rounded p-4 text-center text-gray-500">
                                  Digital Signature Area
                                </div>
                              )}
                              {component.type === 'approval' && approvalSteps.length > 0 && (
                                <div className="border rounded p-4 bg-gray-50">
                                  <h4 className="font-semibold mb-3">Approval Workflow</h4>
                                  <div className="space-y-2">
                                    {approvalSteps.map((step) => (
                                      <div key={step.id} className="flex items-center justify-between p-2 bg-white rounded border">
                                        <div className="flex items-center gap-2">
                                          <CheckCircle className="h-4 w-4 text-green-500" />
                                          <span className="text-sm">{step.name}</span>
                                        </div>
                                        <div className="text-xs text-gray-500">
                                          {step.approvers.length} approver{step.approvers.length !== 1 ? 's' : ''}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {/* Template Footer */}
                    <div className="border-t pt-4 mt-6 text-center text-xs text-gray-500">
                      Generated on {new Date().toLocaleDateString()} | Template: {templateName}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Generation Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Generation Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">{components.length}</div>
                      <div className="text-xs text-gray-500">Components</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{approvalSteps.length}</div>
                      <div className="text-xs text-gray-500">Approval Steps</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {components.filter(c => c.type === 'variable').length}
                      </div>
                      <div className="text-xs text-gray-500">Dynamic Variables</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
