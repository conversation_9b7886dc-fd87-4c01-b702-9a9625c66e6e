"use client";

import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Plus,
  Trash2,
  Edit,
  Eye,
  Settings,
  BarChart3,
  Target
} from "lucide-react";
import { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  evaluationTemplateSchema,
  type EvaluationTemplateInput
} from "@/lib/validations/enhanced-procurement";

interface EvaluationTemplate {
  id: string;
  name: string;
  method: string;
  passGrade?: number;
  criteria: Record<string, {
    weight: number;
    description: string;
    type: "TECHNICAL" | "ADMINISTRATIVE" | "PRICE" | "EXPERIENCE";
    subCriteria?: Array<{
      name: string;
      weight: number;
      description?: string;
    }>;
  }>;
  procurements: Array<{
    id: string;
    procurementNumber: string;
    title: string;
    status: string;
    estimatedValue: number;
    createdAt: string;
  }>;
  stats: {
    totalUsage: number;
    recentUsage: number;
    criteriaCount: number;
    averageWeight: number;
    totalProcurementValue: number;
    criteriaTypes: Record<string, number>;
  };
  canEdit: boolean;
  canDelete: boolean;
}

interface TemplatesResponse {
  templates: EvaluationTemplate[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

async function fetchTemplates(
  page = 1,
  search?: string,
  method?: string
): Promise<TemplatesResponse> {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  if (search) params.append("search", search);
  if (method) params.append("method", method);

  const response = await fetch(`/api/evaluation-templates?${params}`);
  if (!response.ok) throw new Error("Failed to fetch evaluation templates");
  const result = await response.json();
  return result.data;
}

async function createTemplate(data: EvaluationTemplateInput) {
  const response = await fetch("/api/evaluation-templates", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create template");
  }

  return response.json();
}

async function deleteTemplate(id: string) {
  const response = await fetch(`/api/evaluation-templates/${id}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to delete template");
  }

  return response.json();
}

interface EvaluationTemplateManagerProps {
  userRoles: string[];
}

export function EvaluationTemplateManager({ userRoles }: EvaluationTemplateManagerProps) {
  const queryClient = useQueryClient();
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [methodFilter, setMethodFilter] = useState("");
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EvaluationTemplate | null>(null);

  const { data, isLoading, error } = useQuery({
    queryKey: ["evaluation-templates", currentPage, search, methodFilter],
    queryFn: () => fetchTemplates(currentPage, search, methodFilter),
    staleTime: 30000,
  });

  const form = useForm<EvaluationTemplateInput>({
    resolver: zodResolver(evaluationTemplateSchema),
    defaultValues: {
      name: "",
      method: "",
      passGrade: 70,
      criteria: {},
    },
  });

  const createMutation = useMutation({
    mutationFn: createTemplate,
    onSuccess: () => {
      toast.success("Evaluation template created successfully");
      queryClient.invalidateQueries({ queryKey: ["evaluation-templates"] });
      setCreateDialogOpen(false);
      form.reset();
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteTemplate,
    onSuccess: () => {
      toast.success("Evaluation template deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["evaluation-templates"] });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = (data: EvaluationTemplateInput) => {
    createMutation.mutate(data);
  };

  const addCriteria = () => {
    const criteriaKey = `criteria_${Date.now()}`;
    const currentCriteria = (form.getValues() as any).criteria || {};
    form.setValue("criteria", {
      ...currentCriteria,
      [criteriaKey]: {
        weight: 0,
        description: "",
        type: "TECHNICAL",
      }
    });
  };

  const removeCriteria = (key: string) => {
    const currentCriteria = (form.getValues() as any).criteria || {};
    const { [key]: removed, ...rest } = currentCriteria as any;
    form.setValue("criteria", rest);
  };

  const getCriteriaTypeColor = (type: string) => {
    const colors = {
      TECHNICAL: "bg-blue-100 text-blue-800",
      ADMINISTRATIVE: "bg-green-100 text-green-800",
      PRICE: "bg-yellow-100 text-yellow-800",
      EXPERIENCE: "bg-purple-100 text-purple-800",
    };
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const canManageTemplates = userRoles.includes("ADMIN") || userRoles.includes("PROCUREMENT_USER");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Evaluation Templates</h1>
          <p className="text-gray-600">Manage reusable evaluation criteria templates</p>
        </div>

        {canManageTemplates && (
          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create Evaluation Template</DialogTitle>
              </DialogHeader>

              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Template Name *</Label>
                    <Input
                      id="name"
                      {...form.register("name")}
                      placeholder="Enter template name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="method">Evaluation Method *</Label>
                    <Select
                      value={form.watch("method")}
                      onValueChange={(value: string) => form.setValue("method", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="WEIGHTED_SCORING">Weighted Scoring</SelectItem>
                        <SelectItem value="PASS_FAIL">Pass/Fail</SelectItem>
                        <SelectItem value="RANKING">Ranking</SelectItem>
                        <SelectItem value="HYBRID">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="passGrade">Pass Grade (%)</Label>
                  <Input
                    type="number"
                    id="passGrade"
                    {...form.register("passGrade", { valueAsNumber: true })}
                    min="0"
                    max="100"
                    placeholder="70"
                  />
                </div>

                {/* Criteria Section */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <Label>Evaluation Criteria *</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addCriteria}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Criteria
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {Object.entries(form.watch("criteria") || {}).map(([key, criteria]: [string, any]) => (
                      <div key={key} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-medium">Criteria {Object.keys(form.watch("criteria") || {}).indexOf(key) + 1}</h4>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeCriteria(key)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <div>
                            <Label>Weight (%)</Label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={criteria.weight}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                const currentCriteria = (form.getValues() as any).criteria || {};
                                form.setValue("criteria", {
                                  ...currentCriteria,
                                  [key]: {
                                    ...criteria,
                                    weight: Number(e.target.value)
                                  }
                                });
                              }}
                            />
                          </div>
                          <div>
                            <Label>Type</Label>
                            <Select
                              value={criteria.type}
                              onValueChange={(value: string) => {
                                const currentCriteria = (form.getValues() as any).criteria || {};
                                form.setValue("criteria", {
                                  ...currentCriteria,
                                  [key]: {
                                    ...criteria,
                                    type: value as any
                                  }
                                });
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="TECHNICAL">Technical</SelectItem>
                                <SelectItem value="ADMINISTRATIVE">Administrative</SelectItem>
                                <SelectItem value="PRICE">Price</SelectItem>
                                <SelectItem value="EXPERIENCE">Experience</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="md:col-span-1">
                            <Label>Description</Label>
                            <Input
                              value={criteria.description}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                const currentCriteria = (form.getValues() as any).criteria || {};
                                form.setValue("criteria", {
                                  ...currentCriteria,
                                  [key]: {
                                    ...criteria,
                                    description: e.target.value
                                  }
                                });
                              }}
                              placeholder="Criteria description"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Weight Validation */}
                  {Object.keys(form.watch("criteria") || {}).length > 0 && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Total Weight:</span>
                        <span className="font-medium">
                          {Object.values(form.watch("criteria") || {}).reduce((sum, c: any) => sum + (c.weight || 0), 0) as number}%
                        </span>
                      </div>
                      <Progress
                        value={Object.values(form.watch("criteria") || {}).reduce((sum, c: any) => sum + (c.weight || 0), 0)}
                        className="mt-2"
                      />
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex justify-end gap-2 pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setCreateDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={createMutation.isPending}
                  >
                    {createMutation.isPending ? "Creating..." : "Create Template"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search templates..."
                value={search}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
              />
            </div>
            <Select value={methodFilter} onValueChange={setMethodFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Methods</SelectItem>
                <SelectItem value="WEIGHTED_SCORING">Weighted Scoring</SelectItem>
                <SelectItem value="PASS_FAIL">Pass/Fail</SelectItem>
                <SelectItem value="RANKING">Ranking</SelectItem>
                <SelectItem value="HYBRID">Hybrid</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full text-center py-8">Loading templates...</div>
        ) : data?.templates.length === 0 ? (
          <div className="col-span-full text-center py-8 text-gray-500">
            No evaluation templates found
          </div>
        ) : (
          data?.templates.map((template) => (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <Badge variant="outline">{template.method.replace("_", " ")}</Badge>
                </div>
                {template.passGrade && (
                  <p className="text-sm text-gray-600">Pass Grade: {template.passGrade}%</p>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Statistics */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Usage</p>
                    <p className="font-medium">{template.stats.totalUsage} times</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Criteria</p>
                    <p className="font-medium">{template.stats.criteriaCount} items</p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-gray-500">Total Procurement Value</p>
                    <p className="font-medium">{formatCurrency(template.stats.totalProcurementValue)}</p>
                  </div>
                </div>

                {/* Criteria Types */}
                <div>
                  <p className="text-sm text-gray-500 mb-2">Criteria Types:</p>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(template.stats.criteriaTypes).map(([type, count]) => (
                      <Badge key={type} variant="outline" className={`text-xs ${getCriteriaTypeColor(type)}`}>
                        {type}: {count}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Recent Usage */}
                {template.procurements.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Recent Usage:</p>
                    <div className="space-y-1">
                      {template.procurements.slice(0, 2).map((proc) => (
                        <div key={proc.id} className="text-xs text-gray-600">
                          {proc.procurementNumber} - {proc.title}
                        </div>
                      ))}
                      {template.procurements.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{template.procurements.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {template.canEdit && canManageTemplates && (
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                  {template.canDelete && canManageTemplates && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteMutation.mutate(template.id)}
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {data && data.pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-3">
            Page {currentPage} of {data.pagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(data.pagination.totalPages, prev + 1))}
            disabled={currentPage === data.pagination.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
