import { NextRequest } from "next/server";

import { prisma } from "@/lib/db";
import { toPrismaJson } from "@/lib/utils/json";

export interface AuditLogEntry {
  userId?: string;
  sessionId?: string;
  action: string;
  entityType?: string;
  entityId?: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  details?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  requestUrl?: string;
  requestMethod?: string;
  requestHeaders?: Record<string, string>;
  responseStatus?: number;
  executionTime?: number;
  errorMessage?: string;
  metadata?: Record<string, unknown>;
}

export interface UserInteraction {
  type: "PAGE_VIEW" | "SEARCH" | "NAVIGATION" | "CRUD" | "AUTH" | "FILE" | "API" | "WORKFLOW";
  action: string;
  target?: string;
  data?: Record<string, unknown>;
  timestamp: Date;
}

export class ComprehensiveAuditLogger {
  private static instance: ComprehensiveAuditLogger;
  private logQueue: AuditLogEntry[] = [];
  private batchSize = 50;
  private flushInterval = 5000; // 5 seconds

  constructor() {
    // Start batch processing
    setInterval(() => {
      this.flushLogs();
    }, this.flushInterval);
  }

  static getInstance(): ComprehensiveAuditLogger {
    if (!ComprehensiveAuditLogger.instance) {
      ComprehensiveAuditLogger.instance = new ComprehensiveAuditLogger();
    }
    return ComprehensiveAuditLogger.instance;
  }

  // Main audit logging method
  async log(entry: AuditLogEntry): Promise<void> {
    const enrichedEntry = {
      ...entry,
      timestamp: new Date(),
      id: this.generateLogId(),
    };

    // Add to queue for batch processing
    this.logQueue.push(enrichedEntry);

    // Flush immediately for critical actions
    if (this.isCriticalAction(entry.action)) {
      await this.flushLogs();
    }
  }

  // Page view tracking
  async logPageView(
    userId: string | undefined,
    sessionId: string,
    url: string,
    referrer?: string,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      sessionId,
      action: "PAGE_VIEW",
      entityType: "PAGE",
      details: {
        url,
        referrer,
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
      requestUrl: url,
      requestMethod: "GET",
    });
  }

  // Search tracking
  async logSearch(
    userId: string | undefined,
    sessionId: string,
    searchQuery: string,
    searchType: string,
    resultsCount: number,
    filters?: Record<string, unknown>,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      sessionId,
      action: "SEARCH_PERFORMED",
      entityType: "SEARCH",
      details: {
        query: searchQuery,
        searchType,
        resultsCount,
        filters,
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
    });
  }

  // Navigation tracking
  async logNavigation(
    userId: string | undefined,
    sessionId: string,
    fromUrl: string,
    toUrl: string,
    navigationType: "CLICK" | "REDIRECT" | "BACK" | "FORWARD",
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      sessionId,
      action: "NAVIGATION",
      entityType: "NAVIGATION",
      details: {
        fromUrl,
        toUrl,
        navigationType,
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
    });
  }

  // CRUD operations tracking
  async logCrudOperation(
    userId: string,
    action: "CREATE" | "READ" | "UPDATE" | "DELETE",
    entityType: string,
    entityId: string,
    oldValues?: Record<string, unknown>,
    newValues?: Record<string, unknown>,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action: `${entityType}_${action}`,
      entityType,
      entityId,
      oldValues,
      newValues,
      details: {
        operation: action,
        timestamp: new Date().toISOString(),
        changedFields: this.getChangedFields(oldValues, newValues),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
      requestUrl: request?.url,
      requestMethod: request?.method,
    });
  }

  // Authentication events tracking
  async logAuthEvent(
    userId: string | undefined,
    action: "LOGIN_SUCCESS" | "LOGIN_FAILED" | "LOGOUT" | "SESSION_TIMEOUT" | "PASSWORD_CHANGE" | "ACCOUNT_LOCKED",
    details?: Record<string, unknown>,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action,
      entityType: "AUTH",
      details: {
        ...details,
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
    });
  }

  // File operations tracking
  async logFileOperation(
    userId: string,
    action: "UPLOAD" | "DOWNLOAD" | "DELETE" | "VIEW",
    fileName: string,
    fileSize?: number,
    fileType?: string,
    entityType?: string,
    entityId?: string,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action: `FILE_${action}`,
      entityType: "FILE",
      entityId: fileName,
      details: {
        fileName,
        fileSize,
        fileType,
        relatedEntityType: entityType,
        relatedEntityId: entityId,
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
    });
  }

  // API calls tracking
  async logApiCall(
    userId: string | undefined,
    method: string,
    endpoint: string,
    statusCode: number,
    executionTime: number,
    requestBody?: unknown,
    responseBody?: unknown,
    errorMessage?: string,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action: "API_CALL",
      entityType: "API",
      details: {
        endpoint,
        method,
        requestBody: this.sanitizeData(requestBody),
        responseBody: this.sanitizeData(responseBody),
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
      requestUrl: endpoint,
      requestMethod: method,
      responseStatus: statusCode,
      executionTime,
      errorMessage,
    });
  }

  // Workflow state changes tracking
  async logWorkflowStateChange(
    userId: string,
    workflowId: string,
    workflowType: string,
    fromState: string,
    toState: string,
    entityType: string,
    entityId: string,
    approvalComments?: string,
    request?: NextRequest
  ): Promise<void> {
    await this.log({
      userId,
      action: "WORKFLOW_STATE_CHANGE",
      entityType: "WORKFLOW",
      entityId: workflowId,
      oldValues: { state: fromState },
      newValues: { state: toState },
      details: {
        workflowType,
        relatedEntityType: entityType,
        relatedEntityId: entityId,
        approvalComments,
        timestamp: new Date().toISOString(),
      },
      ipAddress: this.extractIpAddress(request),
      userAgent: request?.headers.get("user-agent") || undefined,
    });
  }

  // System integration tracking
  async logIntegrationEvent(
    userId: string | undefined,
    action: string,
    integrationName: string,
    success: boolean,
    details?: Record<string, unknown>,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      userId,
      action: `INTEGRATION_${action}`,
      entityType: "INTEGRATION",
      entityId: integrationName,
      details: {
        integrationName,
        success,
        ...details,
        timestamp: new Date().toISOString(),
      },
      errorMessage,
    });
  }

  // Batch flush logs to database
  private async flushLogs(): Promise<void> {
    if (this.logQueue.length === 0) return;

    const logsToFlush = this.logQueue.splice(0, this.batchSize);

    try {
      await prisma.auditLog.createMany({
        data: logsToFlush.map(log => ({
          userId: log.userId || "anonymous",
          action: log.action as any, // Cast to match Prisma enum
          resource: log.entityType || "UNKNOWN",
          entityType: log.entityType || "UNKNOWN",
          entityId: log.entityId || "",
          oldValues: log.oldValues as any || {},
          newValues: log.newValues as any || {},
          details: log.details as any || {},
          ipAddress: log.ipAddress || "unknown",
          userAgent: log.userAgent || "unknown",
          metadata: {
            requestUrl: log.requestUrl,
            requestMethod: log.requestMethod,
            requestHeaders: log.requestHeaders,
            responseStatus: log.responseStatus,
            executionTime: log.executionTime,
            errorMessage: log.errorMessage,
            ...log.metadata,
          } as any,
        })),
      });
    } catch (error) {
      console.error("Failed to flush audit logs:", error);
      // Re-add failed logs to queue for retry
      this.logQueue.unshift(...logsToFlush);
    }
  }

  // Helper methods
  private extractIpAddress(request?: NextRequest): string {
    if (!request) return "unknown";
    
    return (
      request.headers.get("x-forwarded-for") ||
      request.headers.get("x-real-ip") ||
      request.headers.get("cf-connecting-ip") ||
      "unknown"
    );
  }

  private getChangedFields(oldValues?: Record<string, unknown>, newValues?: Record<string, unknown>): string[] {
    if (!oldValues || !newValues) return [];
    
    const changedFields: string[] = [];
    const allKeys = new Set([...Object.keys(oldValues), ...Object.keys(newValues)]);
    
    for (const key of allKeys) {
      if (JSON.stringify(oldValues[key]) !== JSON.stringify(newValues[key])) {
        changedFields.push(key);
      }
    }
    
    return changedFields;
  }

  private sanitizeData(data: unknown): unknown {
    if (!data) return data;

    // Remove sensitive fields
    const sensitiveFields = ["password", "token", "secret", "key", "credential"];
    const sanitized = JSON.parse(JSON.stringify(data)) as Record<string, unknown>;
    
    const sanitizeObject = (obj: Record<string, unknown>): Record<string, unknown> => {
      if (typeof obj !== "object" || obj === null) return obj;

      for (const key in obj) {
        if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
          obj[key] = "[REDACTED]";
        } else if (typeof obj[key] === "object" && obj[key] !== null) {
          obj[key] = sanitizeObject(obj[key] as Record<string, unknown>);
        }
      }

      return obj;
    };
    
    return sanitizeObject(sanitized);
  }

  private isCriticalAction(action: string): boolean {
    const criticalActions = [
      "LOGIN_FAILED",
      "ACCOUNT_LOCKED",
      "UNAUTHORIZED_ACCESS",
      "DATA_BREACH",
      "SYSTEM_ERROR",
      "SECURITY_VIOLATION",
    ];
    
    return criticalActions.includes(action);
  }

  private generateLogId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get audit logs with filtering
  async getAuditLogs(filters: {
    userId?: string;
    action?: string;
    entityType?: string;
    entityId?: string;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }): Promise<{
    logs: Array<Record<string, unknown>>;
    total: number;
    page: number;
    totalPages: number;
  }> {
    const {
      userId,
      action,
      entityType,
      entityId,
      startDate,
      endDate,
      page = 1,
      limit = 50,
    } = filters;

    const where: Record<string, unknown> = {};

    if (userId) where.userId = userId;
    if (action) where.action = { contains: action, mode: "insensitive" };
    if (entityType) where.entityType = entityType;
    if (entityId) where.entityId = entityId;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt = { ...where.createdAt as Record<string, unknown>, gte: startDate };
      }
      if (endDate) {
        where.createdAt = { ...where.createdAt as Record<string, unknown>, lte: endDate };
      }
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return {
      logs,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  // Additional methods for test compatibility
  async batchLog(logs: Array<{
    userId: string;
    action: string;
    entityType: string;
    entityId: string;
    oldValues?: Record<string, unknown>;
    newValues?: Record<string, unknown>;
    metadata?: Record<string, unknown>;
  }>): Promise<void> {
    try {
      await prisma.auditLog.createMany({
        data: logs.map(log => ({
          userId: log.userId,
          action: log.action as any, // Cast to match Prisma enum
          resource: log.entityType || 'UNKNOWN',
          entityType: log.entityType,
          entityId: log.entityId,
          oldValues: log.oldValues as any,
          newValues: log.newValues as any,
          metadata: log.metadata as any,
          details: {} as any,
          ipAddress: 'unknown',
          userAgent: 'unknown',
        })),
      });
    } catch (error) {
      console.error('Failed to batch log audit events:', error);
    }
  }

  async searchAuditLogs(filters: {
    searchTerm?: string;
    actions?: string[];
    entityTypes?: string[];
    userIds?: string[];
    dateRange?: {
      start: Date;
      end: Date;
    };
    page?: number;
    limit?: number;
  }): Promise<{
    logs: Array<Record<string, unknown>>;
    total: number;
    page: number;
    totalPages: number;
  }> {
    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const where: Record<string, unknown> = {};

    if (filters.actions?.length) {
      where.action = { in: filters.actions };
    }

    if (filters.entityTypes?.length) {
      where.entityType = { in: filters.entityTypes };
    }

    if (filters.userIds?.length) {
      where.userId = { in: filters.userIds };
    }

    if (filters.dateRange) {
      where.createdAt = {
        gte: filters.dateRange.start,
        lte: filters.dateRange.end,
      };
    }

    if (filters.searchTerm) {
      where.OR = [
        { details: { path: ['title'], string_contains: filters.searchTerm } },
        { entityId: { contains: filters.searchTerm } },
      ];
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return {
      logs,
      total,
      page,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getAuditStatistics(): Promise<{
    totalLogs: number;
    averageScore: number;
    actionStats: Array<{action: string; count: number}>;
    severityStats: Array<{severity: string; count: number}>;
    categoryStats: Array<{category: string; count: number}>;
    userStats: Array<{userId: string; count: number}>;
  }> {
    const [totalLogs, averageScore, actionStatsRaw, severityStatsRaw, categoryStatsRaw, userStatsRaw] = await Promise.all([
      prisma.auditLog.count(),
      prisma.auditLog.count(),
      prisma.auditLog.groupBy({
        by: ['action'],
        _count: { action: true },
      }),
      prisma.auditLog.groupBy({
        by: ['severity'],
        _count: { severity: true },
      }),
      prisma.auditLog.groupBy({
        by: ['category'],
        _count: { category: true },
      }),
      prisma.auditLog.groupBy({
        by: ['userEmail'],
        _count: { userEmail: true },
        orderBy: { _count: { userEmail: 'desc' } },
        take: 10,
      }),
    ]);

    return {
      totalLogs,
      averageScore: 0,
      actionStats: actionStatsRaw.map(stat => ({ action: stat.action, count: stat._count.action })),
      severityStats: severityStatsRaw.map(stat => ({ severity: stat.severity || 'UNKNOWN', count: stat._count.severity })),
      categoryStats: categoryStatsRaw.map(stat => ({ category: stat.category || 'UNKNOWN', count: stat._count.category })),
      userStats: userStatsRaw.map(stat => ({ userId: stat.userEmail || 'UNKNOWN', count: stat._count.userEmail })),
    };
  }

  async archiveOldLogs(archiveDate: Date): Promise<{
    archivedCount: number;
    deletedCount: number;
  }> {
    const oldLogs = await prisma.auditLog.findMany({
      where: {
        createdAt: { lt: archiveDate },
      },
    });

    if (oldLogs.length === 0) {
      return { archivedCount: 0, deletedCount: 0 };
    }

    // Archive logs
    await prisma.auditLogArchive.createMany({
      data: oldLogs.map(log => ({
        originalId: log.id,
        userId: log.userId,
        userEmail: log.userEmail,
        userRole: log.userRole,
        action: log.action,
        resource: log.resource,
        resourceId: log.resourceId,
        entityType: log.entityType,
        entityId: log.entityId,
        oldValues: toPrismaJson(log.oldValues),
        newValues: toPrismaJson(log.newValues),
        changedFields: toPrismaJson(log.changedFields),
        details: toPrismaJson(log.details),
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        sessionId: log.sessionId,
        requestId: log.requestId,
        procurementId: log.procurementId,
        workflowStage: log.workflowStage,
        approvalStep: log.approvalStep,
        severity: log.severity,
        category: log.category,
        metadata: toPrismaJson(log.metadata),
        description: log.description,
        timestamp: log.createdAt,
        partitionDate: new Date(log.createdAt.getFullYear(), log.createdAt.getMonth(), log.createdAt.getDate()),
      })),
    });

    // Delete original logs
    const deleteResult = await prisma.auditLog.deleteMany({
      where: {
        createdAt: { lt: archiveDate },
      },
    });

    return {
      archivedCount: oldLogs.length,
      deletedCount: deleteResult.count,
    };
  }

  async cleanupArchivedLogs(retentionDate: Date): Promise<{
    deletedCount: number;
  }> {
    const deleteResult = await prisma.auditLogArchive.deleteMany({
      where: {
        archivedAt: { lt: retentionDate },
      },
    });

    return {
      deletedCount: deleteResult.count,
    };
  }
}

export const auditLogger = ComprehensiveAuditLogger.getInstance();
