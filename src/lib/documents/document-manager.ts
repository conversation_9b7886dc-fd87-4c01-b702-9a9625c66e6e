import { PrismaClient } from '@prisma/client';

// Define types locally since Prisma enums might not be available due to schema issues
export enum DocumentStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED',
  QUARANTINED = 'QUARANTINED'
}

export enum DocumentApprovalStatus {
  NOT_REQUIRED = 'NOT_REQUIRED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED'
}

export enum DocumentPermissionType {
  VIEW = 'VIEW',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  SHARE = 'SHARE',
  APPROVE = 'APPROVE',
  ADMIN = 'ADMIN'
}

export enum DocumentAuditAction {
  CREATED = 'CREATED',
  VIEWED = 'VIEWED',
  DOWNLOADED = 'DOWNLOADED',
  EDITED = 'EDITED',
  DELETED = 'DELETED',
  SHARED = 'SHARED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_REVOKED = 'PERMISSION_REVOKED'
}
import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';

const prisma = new PrismaClient();

export interface DocumentUploadOptions {
  fileName: string;
  fileBuffer: Buffer;
  fileType: string;
  documentType?: string;
  description?: string;
  isConfidential?: boolean;
  requiresApproval?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
  // Entity associations
  procurementId?: string;
  vendorId?: string;
  vendorOfferId?: string;
  poId?: string;
  invoiceId?: string;
  bastId?: string;
  prId?: string;
}

export interface DocumentSearchOptions {
  query?: string;
  documentType?: string;
  status?: DocumentStatus;
  approvalStatus?: DocumentApprovalStatus;
  tags?: string[];
  uploadedById?: string;
  isConfidential?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  // Entity filters
  procurementId?: string;
  vendorId?: string;
  limit?: number;
  offset?: number;
}

export interface DocumentPermissionOptions {
  userId?: string;
  roleId?: string;
  permission: DocumentPermissionType;
  expiresAt?: Date;
}

export class DocumentManager {
  private uploadPath: string;

  constructor(uploadPath: string = 'uploads/documents') {
    this.uploadPath = uploadPath;
  }

  /**
   * Upload and store a document with comprehensive metadata
   */
  async uploadDocument(
    uploadedById: string,
    options: DocumentUploadOptions
  ) {
    try {
      // Generate file checksum for integrity verification
      const checksum = createHash('sha256').update(options.fileBuffer).digest('hex');
      
      // Generate unique file path
      const fileExtension = path.extname(options.fileName);
      const uniqueFileName = `${Date.now()}-${Math.random().toString(36).substring(2)}${fileExtension}`;
      const filePath = path.join(this.uploadPath, uniqueFileName);
      const fileUrl = `/uploads/documents/${uniqueFileName}`;

      // Ensure upload directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // Save file to disk
      await fs.writeFile(filePath, options.fileBuffer);

      // For now, return a mock document while schema is being fixed
      // TODO: Implement actual document creation once schema is fixed
      const document = {
        id: 'temp-doc-id',
        fileName: options.fileName,
        fileUrl,
        fileType: options.fileType,
        documentType: options.documentType,
        description: options.description,
        fileSize: BigInt(options.fileBuffer.length),
        checksum,
        isConfidential: options.isConfidential || false,
        requiresApproval: options.requiresApproval || false,
        approvalStatus: options.requiresApproval ? 'PENDING' : 'NOT_REQUIRED',
        tags: options.tags || [],
        metadata: options.metadata || {},
        uploadedById,
        status: 'ACTIVE',
        uploadedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        // Entity associations
        procurementId: options.procurementId,
        vendorId: options.vendorId,
        vendorOfferId: options.vendorOfferId,
        poId: options.poId,
        invoiceId: options.invoiceId,
        bastId: options.bastId,
        prId: options.prId,
      };

      // Create audit log
      await this.createAuditLog(
        document.id,
        uploadedById,
        DocumentAuditAction.CREATED,
        { fileName: options.fileName, fileSize: options.fileBuffer.length }
      );

      return document;
    } catch (error) {
      console.error('Error uploading document:', error);
      throw new Error('Failed to upload document');
    }
  }

  /**
   * Search documents with comprehensive filtering
   */
  async searchDocuments(options: DocumentSearchOptions = {}) {
    try {
      const where: any = {};

      // Text search
      if (options.query) {
        where.OR = [
          { fileName: { contains: options.query, mode: 'insensitive' } },
          { description: { contains: options.query, mode: 'insensitive' } },
          { tags: { hasSome: [options.query] } }
        ];
      }

      // Filter by document properties
      if (options.documentType) where.documentType = options.documentType;
      if (options.status) where.status = options.status;
      if (options.approvalStatus) where.approvalStatus = options.approvalStatus;
      if (options.uploadedById) where.uploadedById = options.uploadedById;
      if (options.isConfidential !== undefined) where.isConfidential = options.isConfidential;

      // Tag filtering
      if (options.tags && options.tags.length > 0) {
        where.tags = { hasSome: options.tags };
      }

      // Date range filtering
      if (options.dateFrom || options.dateTo) {
        where.createdAt = {};
        if (options.dateFrom) where.createdAt.gte = options.dateFrom;
        if (options.dateTo) where.createdAt.lte = options.dateTo;
      }

      // Entity filtering
      if (options.procurementId) where.procurementId = options.procurementId;
      if (options.vendorId) where.vendorId = options.vendorId;

      // For now, return mock documents while schema is being fixed
      // TODO: Implement actual document search once schema is fixed
      const documents: any[] = [];

      return documents;
    } catch (error) {
      console.error('Error searching documents:', error);
      throw new Error('Failed to search documents');
    }
  }

  /**
   * Get document by ID with permission check
   */
  async getDocument(documentId: string, userId: string) {
    try {
      // For now, return mock document while schema is being fixed
      // TODO: Implement actual document retrieval once schema is fixed
      const document = {
        id: documentId,
        fileName: 'sample-document.pdf',
        fileUrl: '/uploads/sample-document.pdf',
        fileType: 'application/pdf',
        documentType: 'CONTRACT',
        description: 'Sample document',
        uploadedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      if (!document) {
        throw new Error('Document not found');
      }

      // Check if user has permission to view document
      const hasPermission = await this.checkDocumentPermission(
        documentId,
        userId,
        DocumentPermissionType.VIEW
      );

      if (!hasPermission) {
        throw new Error('Access denied');
      }

      // Create audit log for view action
      await this.createAuditLog(
        documentId,
        userId,
        DocumentAuditAction.VIEWED
      );

      return document;
    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  }

  /**
   * Grant document permission to user or role
   */
  async grantPermission(
    documentId: string,
    grantedBy: string,
    options: DocumentPermissionOptions
  ) {
    try {
      // For now, skip permission creation while schema is being fixed
      // TODO: Implement permission creation once schema is fixed
      console.log(`Grant permission: ${options.permission} on document ${documentId} to user ${options.userId} or role ${options.roleId}`);

      return {
        id: 'temp-id',
        documentId,
        userId: options.userId,
        roleId: options.roleId,
        permission: options.permission,
        grantedBy,
        expiresAt: options.expiresAt,
      };
    } catch (error) {
      console.error('Error granting permission:', error);
      throw new Error('Failed to grant permission');
    }
  }

  /**
   * Check if user has specific permission for document
   */
  async checkDocumentPermission(
    documentId: string,
    userId: string,
    permission: DocumentPermissionType
  ): Promise<boolean> {
    try {
      // Check if document exists and get its details
      const document = await prisma.document.findUnique({
        where: { id: documentId },
        // Note: Temporarily simplified due to schema issues
        // include: {
        //   createdBy: true,
        //   permissions: {
        //     where: { userId },
        //   },
        // },
      });

      if (!document) {
        return false;
      }

      // For now, return true for basic access while schema is being fixed
      // TODO: Implement proper permission checking once schema is updated
      console.log(`Permission check for document ${documentId}, user ${userId}, permission ${permission}`);

      // Basic permission logic - can be enhanced once schema is fixed
      switch (permission) {
        case DocumentPermissionType.VIEW:
        case DocumentPermissionType.EDIT:
        case DocumentPermissionType.DELETE:
        case DocumentPermissionType.SHARE:
        case DocumentPermissionType.APPROVE:
        case DocumentPermissionType.ADMIN:
          return true; // Temporary - allow all permissions
        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Create audit log entry
   */
  private async createAuditLog(
    documentId: string,
    userId: string,
    action: DocumentAuditAction,
    details?: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ) {
    try {
      await prisma.auditLog.create({
        data: {
          userId,
          action: action as any, // Cast to match Prisma enum
          resource: 'DOCUMENT',
          entityType: 'DOCUMENT',
          entityId: documentId,
          details: details as any || {},
          ipAddress: ipAddress || 'unknown',
          userAgent: userAgent || 'unknown',
          metadata: {
            documentId,
            action,
            timestamp: new Date().toISOString(),
          } as any,
        },
      });
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error for audit log failures
    }
  }
}

export const documentManager = new DocumentManager();
