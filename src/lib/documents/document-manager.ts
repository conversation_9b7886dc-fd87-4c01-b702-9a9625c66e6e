import { PrismaClient } from '@prisma/client';

// Define types locally since Prisma enums might not be available due to schema issues
export enum DocumentStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  DELETED = 'DELETED',
  QUARANTINED = 'QUARANTINED'
}

export enum DocumentApprovalStatus {
  NOT_REQUIRED = 'NOT_REQUIRED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED'
}

export enum DocumentPermissionType {
  VIEW = 'VIEW',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  SHARE = 'SHARE',
  APPROVE = 'APPROVE',
  ADMIN = 'ADMIN'
}

export enum DocumentAuditAction {
  CREATED = 'CREATED',
  VIEWED = 'VIEWED',
  DOWNLOADED = 'DOWNLOADED',
  EDITED = 'EDITED',
  DELETED = 'DELETED',
  SHARED = 'SHARED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PERMISSION_GRANTED = 'PERMISSION_GRANTED',
  PERMISSION_REVOKED = 'PERMISSION_REVOKED'
}
import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';

const prisma = new PrismaClient();

export interface DocumentUploadOptions {
  fileName: string;
  fileBuffer: Buffer;
  fileType: string;
  documentType?: string;
  description?: string;
  isConfidential?: boolean;
  requiresApproval?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
  // Entity associations
  procurementId?: string;
  vendorId?: string;
  vendorOfferId?: string;
  poId?: string;
  invoiceId?: string;
  bastId?: string;
  prId?: string;
}

export interface DocumentSearchOptions {
  query?: string;
  documentType?: string;
  status?: DocumentStatus;
  approvalStatus?: DocumentApprovalStatus;
  tags?: string[];
  uploadedById?: string;
  isConfidential?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
  // Entity filters
  procurementId?: string;
  vendorId?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'date' | 'size';
  sortOrder?: 'asc' | 'desc';
}

export interface DocumentPermissionOptions {
  userId?: string;
  roleId?: string;
  permission: DocumentPermissionType;
  expiresAt?: Date;
}

export class DocumentManager {
  private uploadPath: string;

  constructor(uploadPath: string = 'uploads/documents') {
    this.uploadPath = uploadPath;
  }

  /**
   * Upload and store a document with comprehensive metadata
   */
  async uploadDocument(
    uploadedById: string,
    options: DocumentUploadOptions
  ) {
    try {
      // Generate file checksum for integrity verification
      const checksum = createHash('sha256').update(options.fileBuffer).digest('hex');
      
      // Generate unique file path
      const fileExtension = path.extname(options.fileName);
      const uniqueFileName = `${Date.now()}-${Math.random().toString(36).substring(2)}${fileExtension}`;
      const filePath = path.join(this.uploadPath, uniqueFileName);
      const fileUrl = `/uploads/documents/${uniqueFileName}`;

      // Ensure upload directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // Save file to disk
      await fs.writeFile(filePath, options.fileBuffer);

      // Create document in database
      const document = await prisma.document.create({
        data: {
          fileName: options.fileName,
          fileUrl,
          fileType: options.fileType,
          documentType: options.documentType,
          description: options.description,
          fileSize: BigInt(options.fileBuffer.length),
          checksum,
          isConfidential: options.isConfidential || false,
          requiresApproval: options.requiresApproval || false,
          approvalStatus: options.requiresApproval ? 'PENDING' : 'NOT_REQUIRED',
          tags: options.tags || [],
          metadata: options.metadata || {},
          uploadedById: uploadedById,
          status: 'ACTIVE',
          // Entity associations
          procurementId: options.procurementId,
          vendorId: options.vendorId,
          vendorOfferId: options.vendorOfferId,
          poId: options.poId,
          invoiceId: options.invoiceId,
          bastId: options.bastId,
          prId: options.prId,
        },
        include: {
          uploadedBy: true,
        }
      });

      // Create audit log
      await this.createAuditLog(
        document.id,
        uploadedById,
        DocumentAuditAction.CREATED,
        { fileName: options.fileName, fileSize: options.fileBuffer.length }
      );

      return document;
    } catch (error) {
      console.error('Error uploading document:', error);
      throw new Error('Failed to upload document');
    }
  }

  /**
   * Search documents with comprehensive filtering
   */
  async searchDocuments(options: DocumentSearchOptions = {}) {
    try {
      const where: any = {};

      // Text search
      if (options.query) {
        where.OR = [
          { fileName: { contains: options.query, mode: 'insensitive' } },
          { description: { contains: options.query, mode: 'insensitive' } },
          { tags: { hasSome: [options.query] } }
        ];
      }

      // Filter by document properties
      if (options.documentType) where.documentType = options.documentType;
      if (options.status) where.status = options.status;
      if (options.approvalStatus) where.approvalStatus = options.approvalStatus;
      if (options.uploadedById) where.uploadedById = options.uploadedById;
      if (options.isConfidential !== undefined) where.isConfidential = options.isConfidential;

      // Tag filtering
      if (options.tags && options.tags.length > 0) {
        where.tags = { hasSome: options.tags };
      }

      // Date range filtering
      if (options.dateFrom || options.dateTo) {
        where.createdAt = {};
        if (options.dateFrom) where.createdAt.gte = options.dateFrom;
        if (options.dateTo) where.createdAt.lte = options.dateTo;
      }

      // Entity filtering
      if (options.procurementId) where.procurementId = options.procurementId;
      if (options.vendorId) where.vendorId = options.vendorId;

      // Execute the search query with proper includes
      const documents = await prisma.document.findMany({
        where,
        include: {
          uploadedBy: {
            select: { id: true, name: true, email: true }
          },
          approvedBy: {
            select: { id: true, name: true, email: true }
          },
          permissions: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              },
              role: {
                select: { id: true, name: true }
              }
            }
          },
          procurement: {
            select: { id: true, title: true, procurementNumber: true }
          },
          vendor: {
            select: { id: true, companyName: true }
          }
        },
        orderBy: options.sortBy === 'name'
          ? { fileName: options.sortOrder || 'asc' }
          : options.sortBy === 'date'
          ? { createdAt: options.sortOrder || 'desc' }
          : options.sortBy === 'size'
          ? { fileSize: options.sortOrder || 'desc' }
          : { createdAt: 'desc' },
        skip: options.offset || 0,
        take: options.limit || 50
      });

      return documents;
    } catch (error) {
      console.error('Error searching documents:', error);
      throw new Error('Failed to search documents');
    }
  }

  /**
   * Get document by ID with permission check
   */
  async getDocument(documentId: string, userId: string) {
    try {
      // Get document from database
      const document = await prisma.document.findUnique({
        where: { id: documentId },
        include: {
          uploadedBy: true,
          permissions: {
            where: {
              OR: [
                { userId },
                {
                  role: {
                    userRoles: {
                      some: { userId }
                    }
                  }
                }
              ]
            },
            include: {
              user: true,
              role: {
                include: {
                  userRoles: true
                }
              }
            }
          }
        }
      });

      if (!document) {
        throw new Error('Document not found');
      }

      // Check if user has permission to view document
      const hasPermission = await this.checkDocumentPermission(
        documentId,
        userId,
        DocumentPermissionType.VIEW
      );

      if (!hasPermission) {
        throw new Error('Access denied');
      }

      // Create audit log for view action
      await this.createAuditLog(
        documentId,
        userId,
        DocumentAuditAction.VIEWED
      );

      return document;
    } catch (error) {
      console.error('Error getting document:', error);
      throw error;
    }
  }

  /**
   * Grant document permission to user or role
   */
  async grantPermission(
    documentId: string,
    grantedBy: string,
    options: DocumentPermissionOptions
  ) {
    try {
      // Create permission in database
      const permission = await prisma.documentPermission.create({
        data: {
          documentId,
          userId: options.userId,
          roleId: options.roleId,
          permission: options.permission,
          grantedBy,
          expiresAt: options.expiresAt,
        },
        include: {
          user: true,
          role: true,
          grantor: true
        }
      });

      return {
        id: permission.id,
        documentId: permission.documentId,
        userId: permission.userId,
        roleId: permission.roleId,
        permission: permission.permission,
        grantedBy: permission.grantedBy,
        expiresAt: permission.expiresAt,
      };
    } catch (error) {
      console.error('Error granting permission:', error);
      throw new Error('Failed to grant permission');
    }
  }

  /**
   * Check if user has specific permission for document
   */
  async checkDocumentPermission(
    documentId: string,
    userId: string,
    permission: DocumentPermissionType
  ): Promise<boolean> {
    try {
      // Check if document exists and get its details
      const document = await prisma.document.findUnique({
        where: { id: documentId },
        include: {
          uploadedBy: true,
          permissions: {
            where: {
              OR: [
                { userId },
                {
                  role: {
                    userRoles: {
                      some: { userId }
                    }
                  }
                }
              ]
            },
            include: {
              user: true,
              role: true
            }
          },
        },
      });

      if (!document) {
        return false;
      }

      // Check if user is the document creator
      if (document.uploadedById === userId) {
        return true; // Document creator has all permissions
      }

      // Check explicit permissions
      const userPermissions = document.permissions.filter((p: any) =>
        p.userId === userId ||
        (p.role && p.role.userRoles.some((ur: any) => ur.userId === userId))
      );

      // Check if user has the required permission level
      for (const userPerm of userPermissions) {
        if (userPerm.permission === permission) {
          // Check if permission has expired
          if (userPerm.expiresAt && userPerm.expiresAt < new Date()) {
            continue;
          }
          return true;
        }

        // Check if user has higher-level permissions that include the requested permission
        if (this.hasHigherPermission(userPerm.permission, permission)) {
          if (userPerm.expiresAt && userPerm.expiresAt < new Date()) {
            continue;
          }
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Check if a permission level includes another permission level
   * Permission hierarchy: ADMIN > APPROVE > SHARE > DELETE > EDIT > VIEW
   */
  private hasHigherPermission(userPermission: any, requiredPermission: DocumentPermissionType): boolean {
    const permissionHierarchy: Record<string, number> = {
      [DocumentPermissionType.VIEW]: 1,
      [DocumentPermissionType.EDIT]: 2,
      [DocumentPermissionType.DELETE]: 3,
      [DocumentPermissionType.SHARE]: 4,
      [DocumentPermissionType.APPROVE]: 5,
      [DocumentPermissionType.ADMIN]: 6,
    };

    return (permissionHierarchy[userPermission] || 0) >= (permissionHierarchy[requiredPermission] || 0);
  }

  /**
   * Create audit log entry
   */
  private async createAuditLog(
    documentId: string,
    userId: string,
    action: DocumentAuditAction,
    details?: Record<string, any>,
    ipAddress?: string,
    userAgent?: string
  ) {
    try {
      await prisma.auditLog.create({
        data: {
          userId,
          action: action as any, // Cast to match Prisma enum
          resource: 'DOCUMENT',
          entityType: 'DOCUMENT',
          entityId: documentId,
          details: details as any || {},
          ipAddress: ipAddress || 'unknown',
          userAgent: userAgent || 'unknown',
          metadata: {
            documentId,
            action,
            timestamp: new Date().toISOString(),
          } as any,
        },
      });
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error for audit log failures
    }
  }
}

export const documentManager = new DocumentManager();
