import { PrismaClient } from '@prisma/client';

import { document<PERSON>anager, DocumentUploadOptions, DocumentSearchOptions } from './document-manager';
import { templateEngine, GenerateDocumentOptions } from './template-engine';

const prisma = new PrismaClient();

export interface DocumentCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  isActive: boolean;
  documentCount?: number;
}

export interface DocumentTag {
  id: string;
  name: string;
  color?: string;
  description?: string;
  usageCount?: number;
}

export interface DocumentStats {
  totalDocuments: number;
  totalSize: number;
  documentsByType: Record<string, number>;
  documentsByStatus: Record<string, number>;
  recentUploads: number;
  pendingApprovals: number;
  byType: Array<{ type: string; count: number }>;
  byStatus: Array<{ status: string; count: number }>;
}

export class DocumentService {
  /**
   * Upload a document with automatic categorization and tagging
   */
  async uploadDocument(
    userId: string,
    file: {
      buffer: Buffer;
      originalname: string;
      mimetype: string;
    },
    options: Partial<DocumentUploadOptions> = {}
  ) {
    try {
      // Auto-detect document type based on file extension and content
      const documentType = this.detectDocumentType(file.originalname, file.mimetype);
      
      // Auto-generate tags based on filename and content
      const autoTags = this.generateAutoTags(file.originalname, options);
      
      const uploadOptions: DocumentUploadOptions = {
        fileName: file.originalname,
        fileBuffer: file.buffer,
        fileType: file.mimetype,
        documentType,
        tags: [...(options.tags || []), ...autoTags],
        ...options,
      };

      const document = await documentManager.uploadDocument(userId, uploadOptions);
      
      // Update document statistics
      await this.updateDocumentStats();
      
      return document;
    } catch (error) {
      console.error('Error in document service upload:', error);
      throw error;
    }
  }

  /**
   * Search documents with enhanced filtering and sorting
   */
  async searchDocuments(
    userId: string,
    options: DocumentSearchOptions & {
      sortBy?: 'uploadedAt' | 'fileName' | 'updatedAt';
      sortOrder?: 'asc' | 'desc';
      includeStats?: boolean;
    } = {}
  ) {
    try {
      const documents = await documentManager.searchDocuments(options);

      // Apply additional sorting if specified
      if (options.sortBy && options.sortOrder && documents.length > 0) {
        documents.sort((a: any, b: any) => {
          const aValue = a[options.sortBy!];
          const bValue = b[options.sortBy!];

          if (options.sortOrder === 'desc') {
            return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
          } else {
            return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
          }
        });
      }

      // Include statistics if requested
      let stats = null;
      if (options.includeStats) {
        stats = await this.getDocumentStats(options);
      }

      return {
        documents,
        stats,
        total: documents.length,
        pagination: {
          page: Math.floor((options.offset || 0) / (options.limit || 10)) + 1,
          limit: options.limit || 10,
          total: documents.length,
          totalPages: Math.ceil(documents.length / (options.limit || 10)),
        },
      };
    } catch (error) {
      console.error('Error in document service search:', error);
      throw error;
    }
  }

  /**
   * Generate document from template with validation
   */
  async generateDocument(
    userId: string,
    options: GenerateDocumentOptions & {
      validateData?: boolean;
      autoSave?: boolean;
    }
  ) {
    try {
      // Validate template data if requested
      if (options.validateData) {
        await this.validateTemplateData(options.templateId, options.data);
      }

      const document = await templateEngine.generateDocument(userId, options);
      
      // Auto-save to document management if requested
      if (options.autoSave) {
        // Convert generated document to uploadable format
        const documentBuffer = Buffer.from(JSON.stringify(document.content));
        
        await this.uploadDocument(userId, {
          buffer: documentBuffer,
          originalname: `${document.name}.json`,
          mimetype: 'application/json',
        }, {
          documentType: document.documentType || undefined,
          description: `Generated from template: ${document.templateName}`,
          metadata: {
            templateId: options.templateId,
            generatedAt: new Date(),
            entityType: options.entityType,
            entityId: options.entityId,
          },
          tags: ['generated', 'template'],
        });
      }

      return document;
    } catch (error) {
      console.error('Error in document service generation:', error);
      throw error;
    }
  }

  /**
   * Get document categories with hierarchy
   */
  async getDocumentCategories(): Promise<DocumentCategory[]> {
    try {
      // Get document categories from database with usage statistics
      const categories = await prisma.documentTemplate.groupBy({
        by: ['category'],
        _count: {
          category: true
        },
        where: {
          isActive: true
        }
      });

      return categories.map(cat => ({
        id: cat.category.toLowerCase().replace(/\s+/g, '-'),
        name: cat.category,
        description: `${cat.category} documents`,
        isActive: true,
        documentCount: cat._count.category
      }));
    } catch (error) {
      console.error('Error getting document categories:', error);
      throw error;
    }
  }

  /**
   * Get popular document tags
   */
  async getDocumentTags(): Promise<DocumentTag[]> {
    try {
      // Get popular tags from documents and templates
      const documentTags = await prisma.document.findMany({
        select: { tags: true },
        where: { status: 'ACTIVE' }
      });

      const templateTags = await prisma.documentTemplate.findMany({
        select: { tags: true },
        where: { isActive: true }
      });

      // Flatten and count tag usage
      const allTags = [...documentTags, ...templateTags].flatMap(item => item.tags);
      const tagCounts = allTags.reduce((acc, tag) => {
        acc[tag] = (acc[tag] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Convert to tag objects with colors
      const colors = ['#ff4444', '#ffaa00', '#00aa00', '#aa0000', '#0066cc', '#6600cc', '#ff6600', '#9900cc'];
      return Object.entries(tagCounts)
        .sort(([,a], [,b]) => b - a) // Sort by usage count
        .slice(0, 20) // Top 20 tags
        .map(([tag, count], index) => ({
          id: tag.toLowerCase().replace(/\s+/g, '-'),
          name: tag,
          color: colors[index % colors.length],
          usageCount: count
        }));
    } catch (error) {
      console.error('Error getting document tags:', error);
      throw error;
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(userIdOrFilters?: string | DocumentSearchOptions, filters?: DocumentSearchOptions): Promise<DocumentStats> {
    // Handle both signatures: getDocumentStats(userId) and getDocumentStats(filters)
    const actualFilters = typeof userIdOrFilters === 'string' ? filters : userIdOrFilters;
    try {
      // Get real document statistics from database
      const whereClause = actualFilters ? {
        ...(actualFilters.documentType && { documentType: actualFilters.documentType }),
        ...(actualFilters.status && { status: actualFilters.status }),
        ...(actualFilters.uploadedById && { uploadedById: actualFilters.uploadedById }),
        ...(actualFilters.isConfidential !== undefined && { isConfidential: actualFilters.isConfidential }),
        ...(actualFilters.dateFrom && { createdAt: { gte: actualFilters.dateFrom } }),
        ...(actualFilters.dateTo && { createdAt: { lte: actualFilters.dateTo } }),
      } : {};

      const [totalCount, totalSizeResult, byType, byStatus, recentCount, pendingCount] = await Promise.all([
        prisma.document.count({ where: whereClause }),
        prisma.document.aggregate({
          where: whereClause,
          _sum: { fileSize: true }
        }),
        prisma.document.groupBy({
          by: ['documentType'],
          _count: { documentType: true },
          where: whereClause
        }),
        prisma.document.groupBy({
          by: ['status'],
          _count: { status: true },
          where: whereClause
        }),
        prisma.document.count({
          where: {
            ...whereClause,
            createdAt: { gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
          }
        }),
        prisma.document.count({
          where: {
            ...whereClause,
            approvalStatus: 'PENDING'
          }
        })
      ]);

      return {
        totalDocuments: totalCount,
        totalSize: Number(totalSizeResult._sum.fileSize || 0),
        documentsByType: byType.reduce((acc, item) => {
          acc[item.documentType || 'unknown'] = item._count.documentType;
          return acc;
        }, {} as Record<string, number>),
        documentsByStatus: byStatus.reduce((acc, item) => {
          acc[item.status] = item._count.status;
          return acc;
        }, {} as Record<string, number>),
        recentUploads: recentCount,
        pendingApprovals: pendingCount,
        byType: byType.map(item => ({
          type: item.documentType || 'unknown',
          count: item._count.documentType
        })),
        byStatus: byStatus.map(item => ({
          status: item.status,
          count: item._count.status
        })),
      };
    } catch (error) {
      console.error('Error getting document stats:', error);
      throw error;
    }
  }

  /**
   * Get document versions
   */
  async getDocumentVersions(documentId: string, userId: string) {
    try {
      // Get document versions using the built-in version control
      const document = await prisma.document.findUnique({
        where: { id: documentId },
        include: {
          versions: {
            include: {
              uploadedBy: {
                select: { id: true, name: true, email: true }
              }
            },
            orderBy: { version: 'desc' }
          },
          uploadedBy: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      if (!document) {
        throw new Error('Document not found');
      }

      // Include the main document as the latest version
      const allVersions = [
        {
          id: document.id,
          documentId: document.id,
          version: document.version,
          fileName: document.fileName,
          createdAt: document.createdAt,
          createdBy: document.uploadedBy,
          changes: 'Current version',
          fileSize: Number(document.fileSize || 0),
          filePath: document.fileUrl
        },
        ...document.versions.map(version => ({
          id: version.id,
          documentId: documentId,
          version: version.version,
          fileName: version.fileName,
          createdAt: version.createdAt,
          createdBy: version.uploadedBy,
          changes: `Version ${version.version}`,
          fileSize: Number(version.fileSize || 0),
          filePath: version.fileUrl
        }))
      ];

      return allVersions.sort((a, b) => b.version - a.version);
    } catch (error) {
      console.error('Error getting document versions:', error);
      throw error;
    }
  }

  /**
   * Create document version
   */
  async createDocumentVersion(
    documentId: string,
    userId: string,
    fileOrContent: string | {
      buffer: Buffer;
      originalname: string;
      mimetype: string;
    },
    changes?: string
  ) {
    try {
      // Get current document to create a new version
      const currentDocument = await prisma.document.findUnique({
        where: { id: documentId },
        include: {
          uploadedBy: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      if (!currentDocument) {
        throw new Error('Document not found');
      }

      const nextVersion = currentDocument.version + 1;

      // Handle file upload or content
      let fileName: string;
      let fileSize: bigint;
      let filePath: string;

      if (typeof fileOrContent === 'string') {
        fileName = `${currentDocument.fileName.replace(/\.[^/.]+$/, '')}-v${nextVersion}.txt`;
        fileSize = BigInt(Buffer.byteLength(fileOrContent, 'utf8'));
        filePath = `documents/versions/${documentId}/${fileName}`;
        // In production, save content to file system or cloud storage
      } else {
        const ext = fileOrContent.originalname.split('.').pop();
        fileName = `${currentDocument.fileName.replace(/\.[^/.]+$/, '')}-v${nextVersion}.${ext}`;
        fileSize = BigInt(fileOrContent.buffer.length);
        filePath = `documents/versions/${documentId}/${fileName}`;
        // In production, save buffer to file system or cloud storage
      }

      // Create new version as a child document
      const newVersion = await prisma.document.create({
        data: {
          fileName,
          fileUrl: filePath,
          fileType: currentDocument.fileType,
          documentType: currentDocument.documentType,
          description: `${currentDocument.description || ''} - Version ${nextVersion}`,
          fileSize,
          version: nextVersion,
          parentId: documentId,
          uploadedById: userId,
          status: currentDocument.status,
          isConfidential: currentDocument.isConfidential,
          tags: currentDocument.tags,
          metadata: {
            ...currentDocument.metadata as object,
            versionChanges: changes || 'Document updated',
            originalDocumentId: documentId
          }
        },
        include: {
          uploadedBy: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      return {
        id: newVersion.id,
        documentId: documentId,
        version: newVersion.version,
        fileName: newVersion.fileName,
        createdAt: newVersion.createdAt,
        createdBy: newVersion.uploadedBy,
        changes: changes || 'Document updated',
        changelog: changes || 'Document updated',
        fileSize: Number(newVersion.fileSize),
        filePath: newVersion.fileUrl
      };
    } catch (error) {
      console.error('Error creating document version:', error);
      throw error;
    }
  }

  /**
   * Bulk upload documents
   */
  async bulkUpload(
    userId: string,
    files: Array<{
      buffer: Buffer;
      originalname: string;
      mimetype: string;
    }>,
    options: Partial<DocumentUploadOptions> = {}
  ) {
    try {
      const results = [];
      for (const file of files) {
        try {
          const result = await this.uploadDocument(userId, file, options);
          results.push({ success: true, document: result });
        } catch (error) {
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Upload failed',
            fileName: file.originalname
          });
        }
      }

      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      return {
        results,
        totalFiles: files.length,
        successCount: successful.length,
        errorCount: failed.length,
        successful,
        failed,
      };
    } catch (error) {
      console.error('Error in bulk upload:', error);
      throw error;
    }
  }

  /**
   * Validate file type
   */
  validateFileType(fileName: string): boolean {
    const allowedExtensions = [
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
      'txt', 'csv', 'jpg', 'jpeg', 'png', 'gif'
    ];

    const extension = fileName.toLowerCase().split('.').pop();
    return allowedExtensions.includes(extension || '');
  }

  /**
   * Validate file size
   */
  validateFileSize(file: Buffer | { buffer?: Buffer; size?: number }): boolean {
    const maxSize = 50 * 1024 * 1024; // 50MB
    let fileSize = 0;

    if (Buffer.isBuffer(file)) {
      fileSize = file.length;
    } else {
      fileSize = file.buffer?.length || file.size || 0;
    }

    return fileSize <= maxSize;
  }

  /**
   * Detect document type based on filename and MIME type
   */
  private detectDocumentType(filename: string, mimeType: string): string {
    const extension = filename.toLowerCase().split('.').pop();
    
    // Map file extensions to document types
    const typeMap: Record<string, string> = {
      'pdf': 'PDF',
      'doc': 'WORD',
      'docx': 'WORD',
      'xls': 'EXCEL',
      'xlsx': 'EXCEL',
      'ppt': 'POWERPOINT',
      'pptx': 'POWERPOINT',
      'txt': 'TEXT',
      'csv': 'CSV',
      'jpg': 'IMAGE',
      'jpeg': 'IMAGE',
      'png': 'IMAGE',
      'gif': 'IMAGE',
      'zip': 'ARCHIVE',
      'rar': 'ARCHIVE',
    };

    return typeMap[extension || ''] || 'OTHER';
  }

  /**
   * Generate automatic tags based on filename and context
   */
  private generateAutoTags(filename: string, options: Partial<DocumentUploadOptions>): string[] {
    const tags: string[] = [];
    const lowerFilename = filename.toLowerCase();

    // Add tags based on filename patterns
    if (lowerFilename.includes('contract')) tags.push('contract');
    if (lowerFilename.includes('invoice')) tags.push('invoice');
    if (lowerFilename.includes('rfq')) tags.push('rfq');
    if (lowerFilename.includes('po')) tags.push('purchase-order');
    if (lowerFilename.includes('bast')) tags.push('bast');
    if (lowerFilename.includes('draft')) tags.push('draft');
    if (lowerFilename.includes('final')) tags.push('final');

    // Add tags based on context
    if (options.procurementId) tags.push('procurement');
    if (options.vendorId) tags.push('vendor');
    if (options.isConfidential) tags.push('confidential');
    if (options.requiresApproval) tags.push('pending-approval');

    return tags;
  }

  /**
   * Validate template data against template requirements
   */
  private async validateTemplateData(templateId: string, data: Record<string, any>): Promise<void> {
    try {
      const template = await templateEngine.getTemplate(templateId);
      const variables = template.variables as any[];

      // Check required variables
      const missingRequired = variables
        .filter(v => v.required && !(v.name in data))
        .map(v => v.name);

      if (missingRequired.length > 0) {
        throw new Error(`Missing required variables: ${missingRequired.join(', ')}`);
      }

      // Validate data types
      for (const variable of variables) {
        if (variable.name in data) {
          const value = data[variable.name];
          const isValid = this.validateDataType(value, variable.type);
          
          if (!isValid) {
            throw new Error(`Invalid data type for variable '${variable.name}'. Expected ${variable.type}.`);
          }
        }
      }
    } catch (error) {
      console.error('Error validating template data:', error);
      throw error;
    }
  }

  /**
   * Validate data type
   */
  private validateDataType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Update document statistics cache
   */
  private async updateDocumentStats(): Promise<void> {
    try {
      // Update document counts and statistics in cache or analytics table
      const stats = await this.getDocumentStats();

      // In production, this could update a statistics cache table
      // or trigger analytics updates
      console.log('Document statistics updated:', {
        totalDocuments: stats.totalDocuments,
        totalSize: stats.totalSize,
        recentUploads: stats.recentUploads,
        pendingApprovals: stats.pendingApprovals
      });
    } catch (error) {
      console.error('Error updating document statistics:', error);
    }
  }
}

export const documentService = new DocumentService();
