import { PrismaClient } from '@prisma/client';

// Define types locally since Prisma enums might not be available due to schema issues
export enum DocumentTemplateType {
  RFQ = 'RFQ',
  CONTRACT = 'CONTRACT',
  PURCHASE_ORDER = 'PURCHASE_ORDER',
  INVOICE = 'INVOICE',
  BAST = 'BAST',
  AANWIJZING = 'AANWIJZING',
  EVALUATION_REPORT = 'EVALUATION_REPORT',
  AWARD_LETTER = 'AWARD_LETTER',
  CUSTOM = 'CUSTOM',
  PURCHASE_REQUISITION = 'PURCHASE_REQUISITION',
  DELIVERY_NOTE = 'DELIVERY_NOTE'
}

export enum DocumentTemplateStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ARCHIVED = 'ARCHIVED'
}

export enum DocumentType {
  RFQ = 'RFQ',
  CONTRACT = 'CONTRACT',
  PURCHASE_ORDER = 'PURCHASE_ORDER',
  INVOICE = 'INVOICE',
  BAST = 'BAST',
  AANWIJZING = 'AANWIJZING',
  EVALUATION_REPORT = 'EVALUATION_REPORT',
  AWARD_LETTER = 'AWARD_LETTER',
  CUSTOM = 'CUSTOM',
  PURCHASE_REQUISITION = 'PURCHASE_REQUISITION',
  DELIVERY_NOTE = 'DELIVERY_NOTE'
}
import Handlebars from 'handlebars';

import { promises as fs } from 'fs';
import path from 'path';

const prisma = new PrismaClient();

export type TemplateVariableValue = string | number | boolean | Date | unknown[] | Record<string, unknown>;

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: TemplateVariableValue;
  options?: string[]; // For select/enum types
}

export interface TemplateComponent {
  id: string;
  type: 'text' | 'table' | 'list' | 'image' | 'signature' | 'conditional' | 'section' | 'page_break' | 'header' | 'footer';
  content: string;
  variables?: string[];
  conditions?: {
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists' | 'not_exists';
    value: TemplateVariableValue;
  }[];
  style?: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold';
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    color?: string;
    backgroundColor?: string;
    margin?: { top?: number; right?: number; bottom?: number; left?: number };
    padding?: { top?: number; right?: number; bottom?: number; left?: number };
  };
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

export interface TemplateLayout {
  header?: TemplateComponent[];
  body: TemplateComponent[];
  footer?: TemplateComponent[];
  styles?: {
    pageSize: 'A4' | 'Letter';
    margins: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
    fonts: {
      primary: string;
      secondary: string;
    };
    colors: {
      primary: string;
      secondary: string;
      text: string;
    };
  };
}

export interface CreateTemplateOptions {
  name: string;
  description?: string;
  type: DocumentTemplateType;
  category: string;
  layout: TemplateLayout;
  variables: TemplateVariable[];
  isActive?: boolean;
  parentTemplateId?: string; // For template inheritance
  overrides?: {
    components?: Partial<TemplateComponent>[];
    variables?: Partial<TemplateVariable>[];
    styles?: Partial<TemplateLayout['styles']>;
  };
}

export interface GenerateDocumentOptions {
  templateId: string;
  name: string;
  data: Record<string, TemplateVariableValue>;
  entityType?: string;
  entityId?: string;
  generatePdf?: boolean;
}

export class DocumentTemplateEngine {
  private templatesPath: string;
  private outputPath: string;

  constructor(
    templatesPath: string = 'templates',
    outputPath: string = 'generated-documents'
  ) {
    this.templatesPath = templatesPath;
    this.outputPath = outputPath;
    this.registerHelpers();
  }

  /**
   * Register Handlebars helpers for template processing
   */
  private registerHelpers() {
    // Date formatting helper
    Handlebars.registerHelper('formatDate', (date: Date, format: string) => {
      if (!date) return '';
      const d = new Date(date);
      
      switch (format) {
        case 'DD/MM/YYYY':
          return d.toLocaleDateString('id-ID');
        case 'DD MMMM YYYY':
          return d.toLocaleDateString('id-ID', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
          });
        case 'YYYY-MM-DD':
          return d.toISOString().split('T')[0];
        default:
          return d.toLocaleDateString('id-ID');
      }
    });

    // Currency formatting helper
    Handlebars.registerHelper('formatCurrency', (amount: number, currency: string = 'IDR') => {
      if (typeof amount !== 'number') return '';
      
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    });

    // Number formatting helper
    Handlebars.registerHelper('formatNumber', (number: number) => {
      if (typeof number !== 'number') return '';
      return new Intl.NumberFormat('id-ID').format(number);
    });

    // Conditional helper
    Handlebars.registerHelper('ifEquals', function(this: any, arg1: any, arg2: any, options: any) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });

    // Array iteration with index
    Handlebars.registerHelper('eachWithIndex', function(array: any[], options: any) {
      let result = '';
      for (let i = 0; i < array.length; i++) {
        result += options.fn({
          ...array[i],
          index: i + 1,
          isFirst: i === 0,
          isLast: i === array.length - 1
        });
      }
      return result;
    });

    // String manipulation helpers
    Handlebars.registerHelper('uppercase', (str: string) => str?.toUpperCase() || '');
    Handlebars.registerHelper('lowercase', (str: string) => str?.toLowerCase() || '');
    Handlebars.registerHelper('capitalize', (str: string) => {
      if (!str) return '';
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    });

    // Advanced conditional helpers
    Handlebars.registerHelper('ifGreaterThan', function(this: any, v1: number, v2: number, options: any) {
      return (v1 > v2) ? options.fn(this) : options.inverse(this);
    });

    Handlebars.registerHelper('ifLessThan', function(this: any, v1: number, v2: number, options: any) {
      return (v1 < v2) ? options.fn(this) : options.inverse(this);
    });

    Handlebars.registerHelper('ifContains', function(this: any, haystack: string, needle: string, options: any) {
      return (haystack && haystack.includes(needle)) ? options.fn(this) : options.inverse(this);
    });

    // Math helpers
    Handlebars.registerHelper('add', (a: number, b: number) => a + b);
    Handlebars.registerHelper('subtract', (a: number, b: number) => a - b);
    Handlebars.registerHelper('multiply', (a: number, b: number) => a * b);
    Handlebars.registerHelper('divide', (a: number, b: number) => b !== 0 ? a / b : 0);
    Handlebars.registerHelper('percentage', (value: number, total: number) =>
      total !== 0 ? ((value / total) * 100).toFixed(2) + '%' : '0%'
    );

    // Date manipulation helpers
    Handlebars.registerHelper('addDays', (date: Date, days: number) => {
      const result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    });

    Handlebars.registerHelper('daysBetween', (date1: Date, date2: Date) => {
      const diffTime = Math.abs(new Date(date2).getTime() - new Date(date1).getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    });

    // Array helpers
    Handlebars.registerHelper('length', (array: any[]) => Array.isArray(array) ? array.length : 0);
    Handlebars.registerHelper('first', (array: any[]) => Array.isArray(array) && array.length > 0 ? array[0] : null);
    Handlebars.registerHelper('last', (array: any[]) => Array.isArray(array) && array.length > 0 ? array[array.length - 1] : null);

    // Object helpers
    Handlebars.registerHelper('keys', (obj: object) => Object.keys(obj || {}));
    Handlebars.registerHelper('values', (obj: object) => Object.values(obj || {}));

    // Procurement-specific helpers
    Handlebars.registerHelper('formatProcurementNumber', (number: string) => {
      return `PROC-${new Date().getFullYear()}-${number.padStart(4, '0')}`;
    });

    Handlebars.registerHelper('formatContractNumber', (number: string) => {
      return `CONT-${new Date().getFullYear()}-${number.padStart(4, '0')}`;
    });

    Handlebars.registerHelper('formatInvoiceNumber', (number: string) => {
      return `INV-${new Date().getFullYear()}-${number.padStart(4, '0')}`;
    });
  }

  /**
   * Create a new document template with inheritance support
   */
  async createTemplate(createdBy: string, options: CreateTemplateOptions) {
    try {
      let finalLayout = options.layout;
      let finalVariables = options.variables;

      // Handle template inheritance
      if (options.parentTemplateId) {
        const parentTemplate = await this.getTemplate(options.parentTemplateId);
        finalLayout = await this.mergeTemplateLayouts(
          parentTemplate.content as unknown as TemplateLayout,
          options.layout,
          options.overrides
        );
        finalVariables = this.mergeTemplateVariables(
          parentTemplate.variables as unknown as TemplateVariable[],
          options.variables,
          options.overrides?.variables
        );
      }

      // For now, skip template creation while schema is being fixed
      // TODO: Implement template creation once schema is fixed
      console.log(`Create template: ${options.name} by user ${createdBy}`);

      return {
        id: 'temp-template-id',
        name: options.name,
        description: options.description,
        type: options.type,
        category: options.category,
        content: finalLayout,
        variables: finalVariables,
        status: DocumentTemplateStatus.DRAFT,
        isActive: options.isActive || false,
        createdBy,
        createdAt: new Date(),
        updatedAt: new Date(),
        parentTemplateId: options.parentTemplateId,
      };
    } catch (error) {
      console.error('Error creating template:', error);
      throw new Error('Failed to create template');
    }
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string) {
    try {
      const template = await prisma.documentTemplate.findUnique({
        where: { id: templateId },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          updater: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          approver: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          versions: {
            orderBy: { version: 'desc' },
            take: 5,
            include: {
              creator: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          }
        }
      });

      if (!template) {
        throw new Error('Template not found');
      }

      return template;
    } catch (error) {
      console.error('Error getting template:', error);
      throw error;
    }
  }

  /**
   * Search templates with filtering
   */
  async searchTemplates(options: {
    query?: string;
    type?: DocumentTemplateType;
    category?: string;
    status?: DocumentTemplateStatus;
    isActive?: boolean;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const where: any = {};

      if (options.query) {
        where.OR = [
          { name: { contains: options.query, mode: 'insensitive' } },
          { description: { contains: options.query, mode: 'insensitive' } },
          { category: { contains: options.query, mode: 'insensitive' } }
        ];
      }

      if (options.type) where.type = options.type;
      if (options.category) where.category = options.category;
      if (options.status) where.status = options.status;
      if (options.isActive !== undefined) where.isActive = options.isActive;

      const templates = await prisma.documentTemplate.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          _count: {
            select: {
              usages: true,
              versions: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: options.limit || 50,
        skip: options.offset || 0,
      });

      return {
        templates,
        pagination: {
          page: Math.floor((options.offset || 0) / (options.limit || 50)) + 1,
          limit: options.limit || 50,
          total: templates.length,
          totalPages: Math.ceil(templates.length / (options.limit || 50)),
        },
      };
    } catch (error) {
      console.error('Error searching templates:', error);
      throw new Error('Failed to search templates');
    }
  }

  /**
   * Generate document from template
   */
  async generateDocument(createdBy: string, options: GenerateDocumentOptions) {
    try {
      // Get template
      const template = await this.getTemplate(options.templateId);
      
      if (!template.isActive) {
        throw new Error('Template is not active');
      }

      // Validate required variables
      const variables = template.variables as unknown as TemplateVariable[];
      const missingVariables = variables
        .filter(v => v.required && !(v.name in options.data))
        .map(v => v.name);

      if (missingVariables.length > 0) {
        throw new Error(`Missing required variables: ${missingVariables.join(', ')}`);
      }

      // Process template layout
      const layout = template.content as unknown as TemplateLayout;
      const processedContent = await this.processTemplateLayout(layout, options.data);

      // Generate document record
      const document = await prisma.generatedDocument.create({
        data: {
          templateId: options.templateId,
          templateName: template.name,
          name: options.name,
          documentType: this.mapTemplateTypeToDocumentType(template.type as DocumentTemplateType),
          entityType: options.entityType,
          entityId: options.entityId,
          data: options.data,
          content: processedContent,
          status: 'GENERATED',
          createdBy,
        },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              type: true,
              category: true,
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      // Generate PDF if requested
      if (options.generatePdf) {
        const pdfUrl = await this.generatePDF(document.id, processedContent);
        await prisma.generatedDocument.update({
          where: { id: document.id },
          data: { 
            pdfUrl,
            generatedAt: new Date(),
          }
        });
      }

      return document;
    } catch (error) {
      console.error('Error generating document:', error);
      throw error;
    }
  }

  /**
   * Process template layout with data
   */
  private async processTemplateLayout(layout: TemplateLayout, data: Record<string, any>) {
    const processedLayout = {
      ...layout,
      header: layout.header ? await this.processComponents(layout.header, data) : undefined,
      body: await this.processComponents(layout.body, data),
      footer: layout.footer ? await this.processComponents(layout.footer, data) : undefined,
    };

    return processedLayout;
  }

  /**
   * Process template components
   */
  private async processComponents(components: TemplateComponent[], data: Record<string, any>) {
    const processedComponents = [];

    for (const component of components) {
      // Check conditions
      if (component.conditions && !this.evaluateConditions(component.conditions, data)) {
        continue; // Skip component if conditions not met
      }

      // Process component content with Handlebars
      const template = Handlebars.compile(component.content);
      const processedContent = template(data);

      processedComponents.push({
        ...component,
        content: processedContent,
      });
    }

    return processedComponents;
  }

  /**
   * Evaluate component conditions
   */
  private evaluateConditions(conditions: any[], data: Record<string, any>): boolean {
    return conditions.every(condition => {
      const fieldValue = this.getNestedValue(data, condition.field);
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === condition.value;
        case 'not_equals':
          return fieldValue !== condition.value;
        case 'contains':
          return String(fieldValue).includes(String(condition.value));
        case 'greater_than':
          return Number(fieldValue) > Number(condition.value);
        case 'less_than':
          return Number(fieldValue) < Number(condition.value);
        default:
          return true;
      }
    });
  }

  /**
   * Get nested object value by dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Map template type to document type
   */
  private mapTemplateTypeToDocumentType(templateType: DocumentTemplateType): DocumentType {
    const mapping: Record<DocumentTemplateType, DocumentType> = {
      RFQ: DocumentType.RFQ,
      CONTRACT: DocumentType.CONTRACT,
      PURCHASE_ORDER: DocumentType.PURCHASE_ORDER,
      INVOICE: DocumentType.INVOICE,
      BAST: DocumentType.BAST,
      AANWIJZING: DocumentType.AANWIJZING,
      EVALUATION_REPORT: DocumentType.EVALUATION_REPORT,
      AWARD_LETTER: DocumentType.AWARD_LETTER,
      CUSTOM: DocumentType.CUSTOM,
      PURCHASE_REQUISITION: DocumentType.PURCHASE_REQUISITION,
      DELIVERY_NOTE: DocumentType.DELIVERY_NOTE,
    };

    return mapping[templateType] || DocumentType.CUSTOM;
  }

  /**
   * Generate PDF from processed content (placeholder implementation)
   */
  private async generatePDF(documentId: string, content: any): Promise<string> {
    // This is a placeholder implementation
    // In a real implementation, you would use a PDF generation library
    // like puppeteer, jsPDF, or a service like Gotenberg
    
    const pdfFileName = `${documentId}.pdf`;
    const pdfPath = path.join(this.outputPath, pdfFileName);
    
    // Ensure output directory exists
    await fs.mkdir(path.dirname(pdfPath), { recursive: true });
    
    // For now, just create a placeholder file
    await fs.writeFile(pdfPath, JSON.stringify(content, null, 2));
    
    return `/generated-documents/${pdfFileName}`;
  }

  /**
   * Merge parent and child template layouts
   */
  private async mergeTemplateLayouts(
    parentLayout: TemplateLayout,
    childLayout: TemplateLayout,
    overrides?: CreateTemplateOptions['overrides']
  ): Promise<TemplateLayout> {
    const mergedLayout: TemplateLayout = {
      header: childLayout.header || parentLayout.header,
      body: [...(parentLayout.body || []), ...(childLayout.body || [])],
      footer: childLayout.footer || parentLayout.footer,
      styles: parentLayout.styles || childLayout.styles ? {
        pageSize: (overrides?.styles?.pageSize || childLayout.styles?.pageSize || parentLayout.styles?.pageSize || 'A4') as 'A4' | 'Letter',
        margins: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20,
          ...parentLayout.styles?.margins,
          ...childLayout.styles?.margins,
          ...overrides?.styles?.margins,
        },
        fonts: {
          primary: 'Arial',
          secondary: 'Times New Roman',
          ...parentLayout.styles?.fonts,
          ...childLayout.styles?.fonts,
          ...overrides?.styles?.fonts,
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          text: '#333333',
          ...parentLayout.styles?.colors,
          ...childLayout.styles?.colors,
          ...overrides?.styles?.colors,
        },
      } : undefined,
    };

    // Apply component overrides
    if (overrides?.components) {
      for (const override of overrides.components) {
        if (override.id) {
          const componentIndex = mergedLayout.body.findIndex(c => c.id === override.id);
          if (componentIndex !== -1) {
            mergedLayout.body[componentIndex] = {
              ...mergedLayout.body[componentIndex],
              ...override,
            };
          }
        }
      }
    }

    return mergedLayout;
  }

  /**
   * Merge parent and child template variables
   */
  private mergeTemplateVariables(
    parentVariables: TemplateVariable[],
    childVariables: TemplateVariable[],
    overrides?: Partial<TemplateVariable>[]
  ): TemplateVariable[] {
    const variableMap = new Map<string, TemplateVariable>();

    // Add parent variables
    parentVariables.forEach(variable => {
      variableMap.set(variable.name, variable);
    });

    // Add/override with child variables
    childVariables.forEach(variable => {
      variableMap.set(variable.name, variable);
    });

    // Apply overrides
    if (overrides) {
      overrides.forEach(override => {
        if (override.name && variableMap.has(override.name)) {
          const existing = variableMap.get(override.name)!;
          variableMap.set(override.name, { ...existing, ...override });
        }
      });
    }

    return Array.from(variableMap.values());
  }

  /**
   * Validate template data against schema
   */
  async validateTemplateData(templateId: string, data: Record<string, any>): Promise<void> {
    try {
      const template = await this.getTemplate(templateId);
      const variables = template.variables as unknown as TemplateVariable[];

      // Check required variables
      const missingRequired = variables
        .filter(v => v.required && !(v.name in data))
        .map(v => v.name);

      if (missingRequired.length > 0) {
        throw new Error(`Missing required variables: ${missingRequired.join(', ')}`);
      }

      // Validate data types and constraints
      for (const variable of variables) {
        if (variable.name in data) {
          const value = data[variable.name];

          // Type validation
          if (!this.validateDataType(value, variable.type)) {
            throw new Error(`Invalid data type for variable '${variable.name}'. Expected ${variable.type}.`);
          }

          // Additional validations based on variable type
          if (variable.type === 'string' && typeof value === 'string') {
            if (variable.options && !variable.options.includes(value)) {
              throw new Error(`Invalid value for variable '${variable.name}'. Must be one of: ${variable.options.join(', ')}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error validating template data:', error);
      throw error;
    }
  }

  /**
   * Validate data type
   */
  private validateDataType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Get template suggestions based on procurement type and stage
   */
  async getTemplateSuggestions(
    procurementType: string,
    stage?: string,
    category?: string
  ): Promise<any[]> {
    try {
      // For now, return mock suggestions
      // TODO: Implement actual template suggestions once schema is fixed
      const suggestions = [
        {
          id: 'rfq-template',
          name: 'Standard RFQ Template',
          description: 'Standard Request for Quotation template',
          type: DocumentTemplateType.RFQ,
          category: 'Procurement',
          isRecommended: procurementType === 'RFQ',
        },
        {
          id: 'contract-template',
          name: 'Service Contract Template',
          description: 'Standard service contract template',
          type: DocumentTemplateType.CONTRACT,
          category: 'Legal',
          isRecommended: stage === 'CONTRACT',
        },
        {
          id: 'po-template',
          name: 'Purchase Order Template',
          description: 'Standard purchase order template',
          type: DocumentTemplateType.PURCHASE_ORDER,
          category: 'Procurement',
          isRecommended: stage === 'AWARD',
        },
      ];

      return suggestions.filter(s =>
        !procurementType || s.type.toLowerCase().includes(procurementType.toLowerCase()) ||
        !category || s.category.toLowerCase().includes(category.toLowerCase())
      );
    } catch (error) {
      console.error('Error getting template suggestions:', error);
      return [];
    }
  }
}

export const templateEngine = new DocumentTemplateEngine();
