import { PrismaClient } from '@prisma/client';

// Define types locally since Prisma enums might not be available due to schema issues
export enum DocumentTemplateType {
  RFQ = 'RFQ',
  CONTRACT = 'CONTRACT',
  PURCHASE_ORDER = 'PURCHASE_ORDER',
  INVOICE = 'INVOICE',
  BAST = 'BAST',
  AANWIJZING = 'AANWIJZING',
  EVALUATION_REPORT = 'EVALUATION_REPORT',
  AWARD_LETTER = 'AWARD_LETTER',
  CUSTOM = 'CUSTOM',
  PURCHASE_REQUISITION = 'PURCHASE_REQUISITION',
  DELIVERY_NOTE = 'DELIVERY_NOTE'
}

export enum DocumentTemplateStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ARCHIVED = 'ARCHIVED'
}

export enum DocumentType {
  RFQ = 'RFQ',
  CONTRACT = 'CONTRACT',
  PURCHASE_ORDER = 'PURCHASE_ORDER',
  INVOICE = 'INVOICE',
  BAST = 'BAST',
  AANWIJZING = 'AANWIJZING',
  EVALUATION_REPORT = 'EVALUATION_REPORT',
  AWARD_LETTER = 'AWARD_LETTER',
  CUSTOM = 'CUSTOM',
  PURCHASE_REQUISITION = 'PURCHASE_REQUISITION',
  DELIVERY_NOTE = 'DELIVERY_NOTE'
}
import Handlebars from 'handlebars';

import { promises as fs } from 'fs';
import path from 'path';

const prisma = new PrismaClient();

export type TemplateVariableValue = string | number | boolean | Date | unknown[] | Record<string, unknown>;

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: TemplateVariableValue;
  options?: string[]; // For select/enum types
}

export interface TemplateComponent {
  id: string;
  type: 'text' | 'table' | 'list' | 'image' | 'signature' | 'conditional' | 'section' | 'page_break' | 'header' | 'footer' | 'container';
  content: string;
  variables?: string[];
  conditions?: {
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists' | 'not_exists';
    value: TemplateVariableValue;
  }[];
  style?: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold';
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    color?: string;
    backgroundColor?: string;
    margin?: { top?: number; right?: number; bottom?: number; left?: number };
    padding?: { top?: number; right?: number; bottom?: number; left?: number };
    borderTop?: string;
    borderBottom?: string;
    paddingTop?: number;
    paddingBottom?: number;
    marginTop?: number;
    marginBottom?: number;
    lineHeight?: number;
    height?: number;
  };
  validation?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
  children?: TemplateComponent[];
  src?: string;
  alt?: string;
  items?: string[];
  headers?: string[];
  rows?: string[] | string;
}

export interface TemplateLayout {
  header?: TemplateComponent[] | { components: TemplateComponent[] };
  body: TemplateComponent[];
  footer?: TemplateComponent[] | { components: TemplateComponent[] };
  styles?: {
    pageSize?: 'A4' | 'Letter';
    margins?: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
    fonts?: {
      primary: string;
      secondary: string;
    };
    colors?: {
      primary: string;
      secondary: string;
      text: string;
      accent?: string;
    };
    spacing?: {
      margin: number;
      padding: number;
    };
  };
}

export interface CreateTemplateOptions {
  name: string;
  description?: string;
  type: DocumentTemplateType;
  category: string;
  layout: TemplateLayout;
  variables: TemplateVariable[];
  isActive?: boolean;
  parentTemplateId?: string; // For template inheritance
  overrides?: {
    components?: Partial<TemplateComponent>[];
    variables?: Partial<TemplateVariable>[];
    styles?: Partial<TemplateLayout['styles']>;
  };
}

export interface GenerateDocumentOptions {
  templateId: string;
  name: string;
  data: Record<string, TemplateVariableValue>;
  entityType?: string;
  entityId?: string;
  generatePdf?: boolean;
}

export class DocumentTemplateEngine {
  private templatesPath: string;
  private outputPath: string;

  constructor(
    templatesPath: string = 'templates',
    outputPath: string = 'generated-documents'
  ) {
    this.templatesPath = templatesPath;
    this.outputPath = outputPath;
    this.registerHelpers();
  }

  /**
   * Register Handlebars helpers for template processing
   */
  private registerHelpers() {
    // Date formatting helper
    Handlebars.registerHelper('formatDate', (date: Date, format: string) => {
      if (!date) return '';
      const d = new Date(date);
      
      switch (format) {
        case 'DD/MM/YYYY':
          return d.toLocaleDateString('id-ID');
        case 'DD MMMM YYYY':
          return d.toLocaleDateString('id-ID', { 
            day: 'numeric', 
            month: 'long', 
            year: 'numeric' 
          });
        case 'YYYY-MM-DD':
          return d.toISOString().split('T')[0];
        default:
          return d.toLocaleDateString('id-ID');
      }
    });

    // Currency formatting helper
    Handlebars.registerHelper('formatCurrency', (amount: number, currency: string = 'IDR') => {
      if (typeof amount !== 'number') return '';
      
      return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount);
    });

    // Number formatting helper
    Handlebars.registerHelper('formatNumber', (number: number) => {
      if (typeof number !== 'number') return '';
      return new Intl.NumberFormat('id-ID').format(number);
    });

    // Conditional helper
    Handlebars.registerHelper('ifEquals', function(this: unknown, arg1: unknown, arg2: unknown, options: Handlebars.HelperOptions) {
      return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
    });

    // Array iteration with index
    Handlebars.registerHelper('eachWithIndex', function(array: unknown[], options: Handlebars.HelperOptions) {
      let result = '';
      for (let i = 0; i < array.length; i++) {
        const item = array[i] as Record<string, unknown>;
        result += options.fn({
          ...item,
          index: i + 1,
          isFirst: i === 0,
          isLast: i === array.length - 1
        });
      }
      return result;
    });

    // String manipulation helpers
    Handlebars.registerHelper('uppercase', (str: string) => str?.toUpperCase() || '');
    Handlebars.registerHelper('lowercase', (str: string) => str?.toLowerCase() || '');
    Handlebars.registerHelper('capitalize', (str: string) => {
      if (!str) return '';
      return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    });

    // Advanced conditional helpers
    Handlebars.registerHelper('ifGreaterThan', function(this: unknown, v1: number, v2: number, options: Handlebars.HelperOptions) {
      return (v1 > v2) ? options.fn(this) : options.inverse(this);
    });

    Handlebars.registerHelper('ifLessThan', function(this: unknown, v1: number, v2: number, options: Handlebars.HelperOptions) {
      return (v1 < v2) ? options.fn(this) : options.inverse(this);
    });

    Handlebars.registerHelper('ifContains', function(this: unknown, haystack: string, needle: string, options: Handlebars.HelperOptions) {
      return (haystack && haystack.includes(needle)) ? options.fn(this) : options.inverse(this);
    });

    // Math helpers
    Handlebars.registerHelper('add', (a: number, b: number) => a + b);
    Handlebars.registerHelper('subtract', (a: number, b: number) => a - b);
    Handlebars.registerHelper('multiply', (a: number, b: number) => a * b);
    Handlebars.registerHelper('divide', (a: number, b: number) => b !== 0 ? a / b : 0);
    Handlebars.registerHelper('percentage', (value: number, total: number) =>
      total !== 0 ? ((value / total) * 100).toFixed(2) + '%' : '0%'
    );

    // Date manipulation helpers
    Handlebars.registerHelper('addDays', (date: Date, days: number) => {
      const result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    });

    Handlebars.registerHelper('daysBetween', (date1: Date, date2: Date) => {
      const diffTime = Math.abs(new Date(date2).getTime() - new Date(date1).getTime());
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    });

    // Array helpers
    Handlebars.registerHelper('length', (array: unknown[]) => Array.isArray(array) ? array.length : 0);
    Handlebars.registerHelper('first', (array: unknown[]) => Array.isArray(array) && array.length > 0 ? array[0] : null);
    Handlebars.registerHelper('last', (array: unknown[]) => Array.isArray(array) && array.length > 0 ? array[array.length - 1] : null);

    // Object helpers
    Handlebars.registerHelper('keys', (obj: object) => Object.keys(obj || {}));
    Handlebars.registerHelper('values', (obj: object) => Object.values(obj || {}));

    // Procurement-specific helpers
    Handlebars.registerHelper('formatProcurementNumber', (number: string) => {
      return `PROC-${new Date().getFullYear()}-${number.padStart(4, '0')}`;
    });

    Handlebars.registerHelper('formatContractNumber', (number: string) => {
      return `CONT-${new Date().getFullYear()}-${number.padStart(4, '0')}`;
    });

    Handlebars.registerHelper('formatInvoiceNumber', (number: string) => {
      return `INV-${new Date().getFullYear()}-${number.padStart(4, '0')}`;
    });
  }

  /**
   * Create a new document template with inheritance support
   */
  async createTemplate(createdBy: string, options: CreateTemplateOptions) {
    try {
      let finalLayout = options.layout;
      let finalVariables = options.variables;

      // Handle template inheritance
      if (options.parentTemplateId) {
        const parentTemplate = await this.getTemplate(options.parentTemplateId);
        finalLayout = await this.mergeTemplateLayouts(
          parentTemplate.content as unknown as TemplateLayout,
          options.layout,
          options.overrides
        );
        finalVariables = this.mergeTemplateVariables(
          parentTemplate.variables as unknown as TemplateVariable[],
          options.variables,
          options.overrides?.variables
        );
      }

      // For now, skip template creation while schema is being fixed
      // TODO: Implement template creation once schema is fixed
      console.log(`Create template: ${options.name} by user ${createdBy}`);

      return {
        id: 'temp-template-id',
        name: options.name,
        description: options.description,
        type: options.type,
        category: options.category,
        content: finalLayout,
        variables: finalVariables,
        status: DocumentTemplateStatus.DRAFT,
        isActive: options.isActive || false,
        createdBy,
        createdAt: new Date(),
        updatedAt: new Date(),
        parentTemplateId: options.parentTemplateId,
      };
    } catch (error) {
      console.error('Error creating template:', error);
      throw new Error('Failed to create template');
    }
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string) {
    try {
      const template = await prisma.documentTemplate.findUnique({
        where: { id: templateId },
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          updater: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          approver: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          versions: {
            orderBy: { version: 'desc' },
            take: 5,
            include: {
              creator: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          }
        }
      });

      if (!template) {
        throw new Error('Template not found');
      }

      return template;
    } catch (error) {
      console.error('Error getting template:', error);
      throw error;
    }
  }

  /**
   * Search templates with filtering
   */
  async searchTemplates(options: {
    query?: string;
    type?: DocumentTemplateType;
    category?: string;
    status?: DocumentTemplateStatus;
    isActive?: boolean;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      const where: Record<string, unknown> = {};

      if (options.query) {
        where.OR = [
          { name: { contains: options.query, mode: 'insensitive' } },
          { description: { contains: options.query, mode: 'insensitive' } },
          { category: { contains: options.query, mode: 'insensitive' } }
        ];
      }

      if (options.type) where.type = options.type;
      if (options.category) where.category = options.category;
      if (options.status) where.status = options.status;
      if (options.isActive !== undefined) where.isActive = options.isActive;

      const templates = await prisma.documentTemplate.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          _count: {
            select: {
              usages: true,
              versions: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: options.limit || 50,
        skip: options.offset || 0,
      });

      return {
        templates,
        pagination: {
          page: Math.floor((options.offset || 0) / (options.limit || 50)) + 1,
          limit: options.limit || 50,
          total: templates.length,
          totalPages: Math.ceil(templates.length / (options.limit || 50)),
        },
      };
    } catch (error) {
      console.error('Error searching templates:', error);
      throw new Error('Failed to search templates');
    }
  }

  /**
   * Generate document from template
   */
  async generateDocument(createdBy: string, options: GenerateDocumentOptions) {
    try {
      // Get template
      const template = await this.getTemplate(options.templateId);
      
      if (!template.isActive) {
        throw new Error('Template is not active');
      }

      // Validate required variables
      const variables = template.variables as unknown as TemplateVariable[];
      const missingVariables = variables
        .filter(v => v.required && !(v.name in options.data))
        .map(v => v.name);

      if (missingVariables.length > 0) {
        throw new Error(`Missing required variables: ${missingVariables.join(', ')}`);
      }

      // Process template layout
      const layout = template.content as unknown as TemplateLayout;
      const processedContent = await this.processTemplateLayout(layout, options.data);

      // Generate document record
      const document = await prisma.generatedDocument.create({
        data: {
          templateId: options.templateId,
          templateName: template.name,
          name: options.name,
          documentType: this.mapTemplateTypeToDocumentType(template.type as DocumentTemplateType),
          entityType: options.entityType,
          entityId: options.entityId,
          data: options.data as any, // Cast for Prisma JSON compatibility
          content: processedContent as any, // Cast for Prisma JSON compatibility
          status: 'GENERATED',
          createdBy,
        },
        include: {
          template: {
            select: {
              id: true,
              name: true,
              type: true,
              category: true,
            }
          },
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        }
      });

      // Generate PDF if requested
      if (options.generatePdf) {
        const pdfUrl = await this.generatePDF(document.id, processedContent);
        await prisma.generatedDocument.update({
          where: { id: document.id },
          data: { 
            pdfUrl,
            generatedAt: new Date(),
          }
        });
      }

      return document;
    } catch (error) {
      console.error('Error generating document:', error);
      throw error;
    }
  }

  /**
   * Process template layout with data
   */
  private async processTemplateLayout(layout: TemplateLayout, data: Record<string, TemplateVariableValue>) {
    const processedLayout = {
      ...layout,
      header: layout.header ? await this.processComponents(
        Array.isArray(layout.header) ? layout.header : layout.header.components,
        data
      ) : undefined,
      body: await this.processComponents(layout.body, data),
      footer: layout.footer ? await this.processComponents(
        Array.isArray(layout.footer) ? layout.footer : layout.footer.components,
        data
      ) : undefined,
    };

    return processedLayout;
  }

  /**
   * Process template components
   */
  private async processComponents(components: TemplateComponent[], data: Record<string, TemplateVariableValue>) {
    const processedComponents = [];

    for (const component of components) {
      // Check conditions
      if (component.conditions && !this.evaluateConditions(component.conditions, data)) {
        continue; // Skip component if conditions not met
      }

      // Process component content with Handlebars
      const template = Handlebars.compile(component.content);
      const processedContent = template(data);

      processedComponents.push({
        ...component,
        content: processedContent,
      });
    }

    return processedComponents;
  }

  /**
   * Evaluate component conditions
   */
  private evaluateConditions(conditions: Array<{field: string; operator: string; value: TemplateVariableValue}>, data: Record<string, TemplateVariableValue>): boolean {
    return conditions.every(condition => {
      const fieldValue = this.getNestedValue(data, condition.field);
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === condition.value;
        case 'not_equals':
          return fieldValue !== condition.value;
        case 'contains':
          return String(fieldValue).includes(String(condition.value));
        case 'greater_than':
          return Number(fieldValue) > Number(condition.value);
        case 'less_than':
          return Number(fieldValue) < Number(condition.value);
        default:
          return true;
      }
    });
  }

  /**
   * Get nested object value by dot notation
   */
  private getNestedValue(obj: Record<string, TemplateVariableValue>, path: string): TemplateVariableValue {
    return path.split('.').reduce((current: any, key) => current?.[key], obj);
  }

  /**
   * Map template type to document type
   */
  private mapTemplateTypeToDocumentType(templateType: DocumentTemplateType): DocumentType {
    const mapping: Record<DocumentTemplateType, DocumentType> = {
      RFQ: DocumentType.RFQ,
      CONTRACT: DocumentType.CONTRACT,
      PURCHASE_ORDER: DocumentType.PURCHASE_ORDER,
      INVOICE: DocumentType.INVOICE,
      BAST: DocumentType.BAST,
      AANWIJZING: DocumentType.AANWIJZING,
      EVALUATION_REPORT: DocumentType.EVALUATION_REPORT,
      AWARD_LETTER: DocumentType.AWARD_LETTER,
      CUSTOM: DocumentType.CUSTOM,
      PURCHASE_REQUISITION: DocumentType.PURCHASE_REQUISITION,
      DELIVERY_NOTE: DocumentType.DELIVERY_NOTE,
    };

    return mapping[templateType] || DocumentType.CUSTOM;
  }

  /**
   * Generate PDF from processed content (placeholder implementation)
   */
  private async generatePDF(documentId: string, content: Record<string, unknown>): Promise<string> {
    // This is a placeholder implementation
    // In a real implementation, you would use a PDF generation library
    // like puppeteer, jsPDF, or a service like Gotenberg
    
    const pdfFileName = `${documentId}.pdf`;
    const pdfPath = path.join(this.outputPath, pdfFileName);
    
    // Ensure output directory exists
    await fs.mkdir(path.dirname(pdfPath), { recursive: true });
    
    // For now, just create a placeholder file
    await fs.writeFile(pdfPath, JSON.stringify(content, null, 2));
    
    return `/generated-documents/${pdfFileName}`;
  }

  /**
   * Merge parent and child template layouts
   */
  private async mergeTemplateLayouts(
    parentLayout: TemplateLayout,
    childLayout: TemplateLayout,
    overrides?: CreateTemplateOptions['overrides']
  ): Promise<TemplateLayout> {
    const mergedLayout: TemplateLayout = {
      header: childLayout.header || parentLayout.header,
      body: [...(parentLayout.body || []), ...(childLayout.body || [])],
      footer: childLayout.footer || parentLayout.footer,
      styles: parentLayout.styles || childLayout.styles ? {
        pageSize: (overrides?.styles?.pageSize || childLayout.styles?.pageSize || parentLayout.styles?.pageSize || 'A4') as 'A4' | 'Letter',
        margins: {
          top: 20,
          right: 20,
          bottom: 20,
          left: 20,
          ...parentLayout.styles?.margins,
          ...childLayout.styles?.margins,
          ...overrides?.styles?.margins,
        },
        fonts: {
          primary: 'Arial',
          secondary: 'Times New Roman',
          ...parentLayout.styles?.fonts,
          ...childLayout.styles?.fonts,
          ...overrides?.styles?.fonts,
        },
        colors: {
          primary: '#000000',
          secondary: '#666666',
          text: '#333333',
          ...parentLayout.styles?.colors,
          ...childLayout.styles?.colors,
          ...overrides?.styles?.colors,
        },
      } : undefined,
    };

    // Apply component overrides
    if (overrides?.components) {
      for (const override of overrides.components) {
        if (override.id) {
          const componentIndex = mergedLayout.body.findIndex(c => c.id === override.id);
          if (componentIndex !== -1) {
            mergedLayout.body[componentIndex] = {
              ...mergedLayout.body[componentIndex],
              ...override,
            };
          }
        }
      }
    }

    return mergedLayout;
  }

  /**
   * Merge parent and child template variables
   */
  private mergeTemplateVariables(
    parentVariables: TemplateVariable[],
    childVariables: TemplateVariable[],
    overrides?: Partial<TemplateVariable>[]
  ): TemplateVariable[] {
    const variableMap = new Map<string, TemplateVariable>();

    // Add parent variables
    parentVariables.forEach(variable => {
      variableMap.set(variable.name, variable);
    });

    // Add/override with child variables
    childVariables.forEach(variable => {
      variableMap.set(variable.name, variable);
    });

    // Apply overrides
    if (overrides) {
      overrides.forEach(override => {
        if (override.name && variableMap.has(override.name)) {
          const existing = variableMap.get(override.name)!;
          variableMap.set(override.name, { ...existing, ...override });
        }
      });
    }

    return Array.from(variableMap.values());
  }

  /**
   * Validate template data against schema
   */
  async validateTemplateData(templateId: string, data: Record<string, TemplateVariableValue>): Promise<void> {
    try {
      const template = await this.getTemplate(templateId);
      const variables = template.variables as unknown as TemplateVariable[];

      // Check required variables
      const missingRequired = variables
        .filter(v => v.required && !(v.name in data))
        .map(v => v.name);

      if (missingRequired.length > 0) {
        throw new Error(`Missing required variables: ${missingRequired.join(', ')}`);
      }

      // Validate data types and constraints
      for (const variable of variables) {
        if (variable.name in data) {
          const value = data[variable.name];

          // Type validation
          if (!this.validateDataType(value, variable.type)) {
            throw new Error(`Invalid data type for variable '${variable.name}'. Expected ${variable.type}.`);
          }

          // Additional validations based on variable type
          if (variable.type === 'string' && typeof value === 'string') {
            if (variable.options && !variable.options.includes(value)) {
              throw new Error(`Invalid value for variable '${variable.name}'. Must be one of: ${variable.options.join(', ')}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error validating template data:', error);
      throw error;
    }
  }

  /**
   * Validate data type
   */
  private validateDataType(value: TemplateVariableValue, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(String(value)));
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Get template suggestions based on procurement type and stage
   */
  async getTemplateSuggestions(
    procurementType: string,
    stage?: string,
    category?: string
  ): Promise<Array<{id: string; name: string; description: string; type: string; category: string; isRecommended?: boolean}>> {
    try {
      // Get templates from database based on procurement context
      // Note: Simplified query due to schema issues
      const templates = await prisma.documentTemplate.findMany({
        where: {
          isActive: true,
          // TODO: Add proper filtering once schema is updated
          // ...(procurementType && { type: procurementType }),
          // ...(category && { category }),
        },
        orderBy: [
          // { isDefault: 'desc' }, // Commented out due to schema issues
          { updatedAt: 'desc' }
        ]
      });

      // Convert to suggestion format with stage-based recommendations
      const suggestions = templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description || '',
        type: template.type,
        category: template.category || 'General',
        isRecommended: this.isTemplateRecommendedForStage(template, stage, procurementType)
      }));

      // Add dynamic templates based on stage and approval workflow
      const dynamicSuggestions = await this.generateDynamicTemplateSuggestions(
        procurementType,
        stage,
        category
      );

      return [...suggestions, ...dynamicSuggestions];
    } catch (error) {
      console.error('Error getting template suggestions:', error);
      return [];
    }
  }

  /**
   * Generate dynamic template suggestions based on procurement context
   */
  private async generateDynamicTemplateSuggestions(
    procurementType?: string,
    stage?: string,
    category?: string
  ): Promise<Array<{id: string; name: string; description: string; type: string; category: string; isRecommended?: boolean}>> {
    const suggestions = [];

    // Stage-specific template suggestions
    switch (stage?.toLowerCase()) {
      case 'announcement':
        suggestions.push({
          id: 'dynamic-announcement',
          name: `${procurementType || 'Procurement'} Announcement`,
          description: `Dynamic announcement template for ${procurementType || 'procurement'} process`,
          type: 'ANNOUNCEMENT',
          category: category || 'Procurement',
          isRecommended: true
        });
        break;

      case 'submission':
        suggestions.push({
          id: 'dynamic-submission-guide',
          name: 'Submission Guidelines',
          description: 'Dynamic submission guidelines with vendor requirements',
          type: 'SUBMISSION_GUIDE',
          category: category || 'Procurement',
          isRecommended: true
        });
        break;

      case 'evaluation':
        suggestions.push({
          id: 'dynamic-evaluation-report',
          name: 'Evaluation Report',
          description: 'Dynamic evaluation report with scoring matrix',
          type: 'EVALUATION_REPORT',
          category: category || 'Procurement',
          isRecommended: true
        });
        break;

      case 'award':
        suggestions.push({
          id: 'dynamic-award-letter',
          name: 'Award Notification',
          description: 'Dynamic award letter with contract terms',
          type: 'AWARD_LETTER',
          category: category || 'Legal',
          isRecommended: true
        });
        break;

      case 'contract':
        suggestions.push({
          id: 'dynamic-contract',
          name: `${category || 'Service'} Contract`,
          description: `Dynamic contract template for ${category?.toLowerCase() || 'service'} procurement`,
          type: 'CONTRACT',
          category: 'Legal',
          isRecommended: true
        });
        break;
    }

    return suggestions;
  }

  /**
   * Check if template is recommended for specific stage
   */
  private isTemplateRecommendedForStage(
    template: any,
    stage?: string,
    procurementType?: string
  ): boolean {
    if (!stage) return false;

    const metadata = template.metadata as any;
    const recommendedStages = metadata?.recommendedStages || [];
    const supportedTypes = metadata?.supportedProcurementTypes || [];

    return recommendedStages.includes(stage.toUpperCase()) ||
           (procurementType && supportedTypes.includes(procurementType.toUpperCase())) ||
           template.type === stage.toUpperCase();
  }
  /**
   * Generate dynamic template based on procurement stage and approval workflow
   */
  async generateDynamicTemplate(
    procurementId: string,
    stageType: string,
    approvalWorkflowId?: string,
    customizations?: Record<string, unknown>
  ): Promise<TemplateLayout> {
    try {
      // Get procurement details
      const procurement = await prisma.procurement.findUnique({
        where: { id: procurementId },
        // Note: Simplified include due to schema issues
        // include: {
        //   category: true,
        //   createdBy: true,
        //   company: true
        // }
      });

      if (!procurement) {
        throw new Error('Procurement not found');
      }

      // Get approval workflow if provided
      let approvalWorkflow = null;
      if (approvalWorkflowId) {
        approvalWorkflow = await prisma.approvalWorkflow.findUnique({
          where: { id: approvalWorkflowId },
          include: {
            steps: {
              // include: {
              //   approvers: true // Commented out due to schema issues
              // },
              orderBy: { sequence: 'asc' }
            }
          }
        });
      }

      // Generate template based on stage type
      const baseTemplate = await this.createStageBaseTemplate(
        stageType,
        procurement,
        approvalWorkflow
      );

      // Apply customizations
      if (customizations) {
        return this.applyTemplateCustomizations(baseTemplate, customizations);
      }

      return baseTemplate;
    } catch (error) {
      console.error('Error generating dynamic template:', error);
      throw error;
    }
  }

  /**
   * Create base template for specific procurement stage
   */
  private async createStageBaseTemplate(
    stageType: string,
    procurement: any,
    approvalWorkflow?: any
  ): Promise<TemplateLayout> {
    const baseLayout: TemplateLayout = {
      header: {
        components: [
          {
            id: 'company-header',
            type: 'container',
            content: '',
            style: {
              textAlign: 'center',
              marginBottom: 20,
              borderBottom: '2px solid #333',
              paddingBottom: 10
            },
            children: [
              {
                id: 'company-logo',
                type: 'image',
                content: '',
                src: '{{company.logoUrl}}',
                alt: '{{company.name}} Logo',
                style: { height: 60, marginBottom: 10 }
              },
              {
                id: 'company-name',
                type: 'text',
                content: '{{company.name}}',
                style: { fontSize: 18, fontWeight: 'bold' }
              },
              {
                id: 'document-title',
                type: 'text',
                content: this.getStageDocumentTitle(stageType),
                style: { fontSize: 16, fontWeight: 'bold', marginTop: 10 }
              }
            ]
          }
        ]
      },
      body: await this.createStageBodyComponents(stageType, procurement, approvalWorkflow),
      footer: {
        components: [
          {
            id: 'document-footer',
            type: 'container',
            content: '',
            style: {
              borderTop: '1px solid #ccc',
              paddingTop: 10,
              marginTop: 20,
              fontSize: 10,
              textAlign: 'center'
            },
            children: [
              {
                id: 'generation-info',
                type: 'text',
                content: 'Generated on {{_meta.generatedAt}} | Document ID: {{documentId}}',
                style: { color: '#666' }
              }
            ]
          }
        ]
      },
      styles: {
        fonts: {
          primary: 'Arial, sans-serif',
          secondary: 'Times New Roman, serif'
        },
        colors: {
          primary: '#333333',
          secondary: '#666666',
          text: '#333333',
          accent: '#0066cc'
        },
        spacing: {
          margin: 20,
          padding: 15
        }
      }
    };

    return baseLayout;
  }

  /**
   * Create body components based on stage type
   */
  private async createStageBodyComponents(
    stageType: string,
    procurement: any,
    approvalWorkflow?: any
  ): Promise<TemplateComponent[]> {
    const components: TemplateComponent[] = [];

    // Add procurement basic info
    components.push({
      id: 'procurement-info',
      type: 'container',
      content: '',
      children: [
        {
          id: 'procurement-title',
          type: 'text',
          content: 'Procurement: {{procurement.title}}',
          style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 }
        },
        {
          id: 'procurement-number',
          type: 'text',
          content: 'Number: {{procurement.procurementNumber}}',
          style: { marginBottom: 5 }
        },
        {
          id: 'procurement-category',
          type: 'text',
          content: 'Category: {{procurement.category.name}}',
          style: { marginBottom: 5 }
        },
        {
          id: 'procurement-value',
          type: 'text',
          content: 'Estimated Value: {{formatCurrency procurement.estimatedValue}}',
          style: { marginBottom: 15 }
        }
      ]
    });

    // Add stage-specific components
    switch (stageType.toLowerCase()) {
      case 'announcement':
        components.push(...this.createAnnouncementComponents());
        break;
      case 'submission':
        components.push(...this.createSubmissionComponents());
        break;
      case 'evaluation':
        components.push(...this.createEvaluationComponents());
        break;
      case 'award':
        components.push(...this.createAwardComponents());
        break;
      case 'contract':
        components.push(...this.createContractComponents());
        break;
    }

    // Add approval workflow components if provided
    if (approvalWorkflow) {
      components.push(...this.createApprovalComponents(approvalWorkflow));
    }

    return components;
  }

  /**
   * Get document title based on stage type
   */
  private getStageDocumentTitle(stageType: string): string {
    const titles: Record<string, string> = {
      'announcement': 'PROCUREMENT ANNOUNCEMENT',
      'submission': 'SUBMISSION GUIDELINES',
      'evaluation': 'EVALUATION REPORT',
      'award': 'AWARD NOTIFICATION',
      'contract': 'CONTRACT AGREEMENT'
    };

    return titles[stageType.toLowerCase()] || 'PROCUREMENT DOCUMENT';
  }

  /**
   * Create announcement stage components
   */
  private createAnnouncementComponents(): TemplateComponent[] {
    return [
      {
        id: 'announcement-details',
        type: 'container',
        content: '',
        children: [
          {
            id: 'announcement-title',
            type: 'text',
            content: 'PROCUREMENT ANNOUNCEMENT',
            style: { fontSize: 16, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 }
          },
          {
            id: 'announcement-description',
            type: 'text',
            content: '{{procurement.description}}',
            style: { marginBottom: 15, lineHeight: 1.5 }
          },
          {
            id: 'submission-deadline',
            type: 'text',
            content: 'Submission Deadline: {{formatDate procurement.submissionDeadline}}',
            style: { fontWeight: 'bold', color: '#d32f2f', marginBottom: 10 }
          },
          {
            id: 'contact-info',
            type: 'text',
            content: 'Contact Person: {{procurement.contactPerson}} ({{procurement.contactEmail}})',
            style: { marginBottom: 15 }
          }
        ]
      },
      {
        id: 'requirements-section',
        type: 'container',
        content: '',
        children: [
          {
            id: 'requirements-title',
            type: 'text',
            content: 'VENDOR REQUIREMENTS',
            style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 }
          },
          {
            id: 'requirements-list',
            type: 'list',
            content: '',
            items: [
              'Valid business license',
              'Tax compliance certificate',
              'Financial statements (last 2 years)',
              'Technical capability documentation',
              'Previous project references'
            ],
            style: { marginBottom: 15 }
          }
        ]
      }
    ];
  }

  /**
   * Create submission stage components
   */
  private createSubmissionComponents(): TemplateComponent[] {
    return [
      {
        id: 'submission-guidelines',
        type: 'container',
        content: '',
        children: [
          {
            id: 'guidelines-title',
            type: 'text',
            content: 'SUBMISSION GUIDELINES',
            style: { fontSize: 16, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 }
          },
          {
            id: 'submission-format',
            type: 'text',
            content: 'All submissions must be in PDF format and include the following documents:',
            style: { marginBottom: 10 }
          },
          {
            id: 'required-documents',
            type: 'list',
            content: '',
            items: [
              'Technical proposal with detailed specifications',
              'Commercial proposal with pricing breakdown',
              'Company profile and credentials',
              'Project timeline and methodology',
              'Quality assurance plan'
            ],
            style: { marginBottom: 15 }
          }
        ]
      },
      {
        id: 'evaluation-criteria',
        type: 'container',
        content: '',
        children: [
          {
            id: 'criteria-title',
            type: 'text',
            content: 'EVALUATION CRITERIA',
            style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 }
          },
          {
            id: 'criteria-table',
            type: 'table',
            content: '',
            headers: ['Criteria', 'Weight', 'Description'],
            rows: [
              'Technical Capability,40%,Technical expertise and solution quality',
              'Commercial Proposal,35%,Price competitiveness and value for money',
              'Company Experience,15%,Relevant project experience and references',
              'Timeline & Methodology,10%,Project approach and delivery timeline'
            ],
            style: { marginBottom: 15 }
          }
        ]
      }
    ];
  }

  /**
   * Create evaluation stage components
   */
  private createEvaluationComponents(): TemplateComponent[] {
    return [
      {
        id: 'evaluation-summary',
        type: 'container',
        content: '',
        children: [
          {
            id: 'evaluation-title',
            type: 'text',
            content: 'VENDOR EVALUATION REPORT',
            style: { fontSize: 16, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 }
          },
          {
            id: 'evaluation-period',
            type: 'text',
            content: 'Evaluation Period: {{formatDate evaluation.startDate}} - {{formatDate evaluation.endDate}}',
            style: { marginBottom: 10 }
          },
          {
            id: 'total-submissions',
            type: 'text',
            content: 'Total Submissions Received: {{evaluation.totalSubmissions}}',
            style: { marginBottom: 15 }
          }
        ]
      },
      {
        id: 'scoring-matrix',
        type: 'container',
        content: '',
        children: [
          {
            id: 'scoring-title',
            type: 'text',
            content: 'EVALUATION RESULTS',
            style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 }
          },
          {
            id: 'vendor-scores',
            type: 'table',
            content: '',
            headers: ['Vendor', 'Technical Score', 'Commercial Score', 'Experience Score', 'Total Score', 'Rank'],
            rows: '{{#each evaluation.vendorScores}}' +
                  '[{{vendor.name}}, {{technicalScore}}, {{commercialScore}}, {{experienceScore}}, {{totalScore}}, {{rank}}]' +
                  '{{/each}}',
            style: { marginBottom: 15 }
          }
        ]
      }
    ];
  }

  /**
   * Create award stage components
   */
  private createAwardComponents(): TemplateComponent[] {
    return [
      {
        id: 'award-notification',
        type: 'text',
        content: 'AWARD NOTIFICATION',
        style: { fontSize: 16, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 }
      },
      {
        id: 'winning-vendor',
        type: 'text',
        content: 'Winning Vendor: {{award.winningVendor.name}}',
        style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 }
      },
      {
        id: 'award-value',
        type: 'text',
        content: 'Contract Value: {{formatCurrency award.contractValue}}',
        style: { marginBottom: 15 }
      }
    ];
  }

  /**
   * Create contract stage components
   */
  private createContractComponents(): TemplateComponent[] {
    return [
      {
        id: 'contract-header',
        type: 'text',
        content: 'CONTRACT AGREEMENT',
        style: { fontSize: 16, fontWeight: 'bold', textAlign: 'center', marginBottom: 20 }
      },
      {
        id: 'contract-parties',
        type: 'text',
        content: 'Between {{company.name}} and {{vendor.name}}',
        style: { fontSize: 14, marginBottom: 15 }
      },
      {
        id: 'contract-terms',
        type: 'text',
        content: 'Contract Terms and Conditions will be detailed here...',
        style: { marginBottom: 15 }
      }
    ];
  }

  /**
   * Create approval workflow components
   */
  private createApprovalComponents(approvalWorkflow: any): TemplateComponent[] {
    return [
      {
        id: 'approval-workflow',
        type: 'text',
        content: 'APPROVAL WORKFLOW',
        style: { fontSize: 14, fontWeight: 'bold', marginBottom: 10 }
      },
      {
        id: 'approval-steps',
        type: 'list',
        content: '',
        items: approvalWorkflow.steps?.map((step: any) =>
          `${step.name}: ${step.approvers?.map((a: any) => a.name).join(', ') || 'No approvers'}`
        ) || [],
        style: { marginBottom: 15 }
      }
    ];
  }

  /**
   * Apply template customizations
   */
  private applyTemplateCustomizations(
    baseTemplate: TemplateLayout,
    customizations: Record<string, unknown>
  ): TemplateLayout {
    // Apply customizations to the base template
    const customizedTemplate = { ...baseTemplate };

    if (customizations.styles) {
      customizedTemplate.styles = {
        ...customizedTemplate.styles,
        ...customizations.styles as any
      };
    }

    return customizedTemplate;
  }
}

// Export singleton instance
export const templateEngine = new DocumentTemplateEngine();
