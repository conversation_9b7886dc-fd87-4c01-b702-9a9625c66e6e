

import { procurementDocumentIntegration } from './procurement-integration';



export interface WorkflowEvent {
  type: 'STAGE_STARTED' | 'STAGE_COMPLETED' | 'DOCUMENT_UPLOADED' | 'DOCUMENT_APPROVED' | 'DOCUMENT_REJECTED';
  procurementId: string;
  stageId: string;
  stageType: string;
  userId: string;
  metadata?: Record<string, any>;
}

export interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    eventType: WorkflowEvent['type'];
    stageType?: string;
    conditions?: {
      field: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
      value: any;
    }[];
  };
  actions: {
    type: 'GENERATE_DOCUMENT' | 'SEND_NOTIFICATION' | 'UPDATE_STATUS' | 'CREATE_TASK';
    config: Record<string, any>;
  }[];
  isActive: boolean;
}

export class WorkflowAutomation {
  private automationRules: AutomationRule[] = [
    {
      id: 'auto-generate-announcement',
      name: 'Auto-generate Announcement Documents',
      description: 'Automatically generate announcement and RFQ documents when announcement stage starts',
      trigger: {
        eventType: 'STAGE_STARTED',
        stageType: 'ANNOUNCEMENT',
      },
      actions: [
        {
          type: 'GENERATE_DOCUMENT',
          config: {
            documentTypes: ['ANNOUNCEMENT', 'RFQ'],
            autoApprove: false,
          },
        },
        {
          type: 'SEND_NOTIFICATION',
          config: {
            recipients: ['procurement_team'],
            message: 'Announcement documents have been generated and are pending approval',
          },
        },
      ],
      isActive: true,
    },
    {
      id: 'auto-generate-evaluation',
      name: 'Auto-generate Evaluation Template',
      description: 'Generate evaluation template when evaluation stage starts',
      trigger: {
        eventType: 'STAGE_STARTED',
        stageType: 'EVALUATION',
      },
      actions: [
        {
          type: 'GENERATE_DOCUMENT',
          config: {
            documentTypes: ['EVALUATION_REPORT'],
            autoApprove: false,
          },
        },
      ],
      isActive: true,
    },
    {
      id: 'auto-generate-award',
      name: 'Auto-generate Award Documents',
      description: 'Generate award letter and purchase order when award stage starts',
      trigger: {
        eventType: 'STAGE_STARTED',
        stageType: 'AWARD',
      },
      actions: [
        {
          type: 'GENERATE_DOCUMENT',
          config: {
            documentTypes: ['AWARD_LETTER', 'PURCHASE_ORDER'],
            autoApprove: false,
          },
        },
        {
          type: 'SEND_NOTIFICATION',
          config: {
            recipients: ['winning_vendor', 'procurement_team'],
            message: 'Award documents have been generated',
          },
        },
      ],
      isActive: true,
    },
    {
      id: 'auto-generate-contract',
      name: 'Auto-generate Contract',
      description: 'Generate contract when contract stage starts',
      trigger: {
        eventType: 'STAGE_STARTED',
        stageType: 'CONTRACT',
      },
      actions: [
        {
          type: 'GENERATE_DOCUMENT',
          config: {
            documentTypes: ['CONTRACT'],
            autoApprove: false,
          },
        },
      ],
      isActive: true,
    },
  ];

  /**
   * Process workflow event and execute automation rules
   */
  async processWorkflowEvent(event: WorkflowEvent): Promise<void> {
    try {
      console.log(`Processing workflow event: ${event.type} for procurement ${event.procurementId}, stage ${event.stageType}`);

      // Find matching automation rules
      const matchingRules = this.automationRules.filter(rule => 
        rule.isActive && this.doesEventMatchRule(event, rule)
      );

      // Execute actions for each matching rule
      for (const rule of matchingRules) {
        try {
          await this.executeRuleActions(rule, event);
        } catch (error) {
          console.error(`Error executing rule ${rule.id}:`, error);
          // Continue with other rules even if one fails
        }
      }
    } catch (error) {
      console.error('Error processing workflow event:', error);
      throw error;
    }
  }

  /**
   * Check if event matches automation rule
   */
  private doesEventMatchRule(event: WorkflowEvent, rule: AutomationRule): boolean {
    // Check event type
    if (rule.trigger.eventType !== event.type) {
      return false;
    }

    // Check stage type if specified
    if (rule.trigger.stageType && rule.trigger.stageType !== event.stageType) {
      return false;
    }

    // Check additional conditions if specified
    if (rule.trigger.conditions) {
      for (const condition of rule.trigger.conditions) {
        const fieldValue = this.getEventFieldValue(event, condition.field);
        if (!this.evaluateCondition(fieldValue, condition.operator, condition.value)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Execute actions for a matched rule
   */
  private async executeRuleActions(rule: AutomationRule, event: WorkflowEvent): Promise<void> {
    console.log(`Executing rule: ${rule.name}`);

    for (const action of rule.actions) {
      try {
        switch (action.type) {
          case 'GENERATE_DOCUMENT':
            await this.executeGenerateDocumentAction(action.config, event);
            break;
          case 'SEND_NOTIFICATION':
            await this.executeSendNotificationAction(action.config, event);
            break;
          case 'UPDATE_STATUS':
            await this.executeUpdateStatusAction(action.config, event);
            break;
          case 'CREATE_TASK':
            await this.executeCreateTaskAction(action.config, event);
            break;
          default:
            console.warn(`Unknown action type: ${action.type}`);
        }
      } catch (error) {
        console.error(`Error executing action ${action.type}:`, error);
        // Continue with other actions even if one fails
      }
    }
  }

  /**
   * Execute document generation action
   */
  private async executeGenerateDocumentAction(
    config: Record<string, any>,
    event: WorkflowEvent
  ): Promise<void> {
    try {
      const documentTypes = config.documentTypes as string[];
      
      if (!documentTypes || documentTypes.length === 0) {
        console.warn('No document types specified for generation');
        return;
      }

      // Generate documents for the stage
      const generatedDocuments = await procurementDocumentIntegration.generateStageDocuments(
        {
          procurementId: event.procurementId,
          stageId: event.stageId,
          stageType: event.stageType,
          procurementData: event.metadata?.procurementData,
          vendorData: event.metadata?.vendorData,
          additionalData: event.metadata?.additionalData,
        },
        event.userId
      );

      console.log(`Generated ${generatedDocuments.length} documents for stage ${event.stageType}`);

      // Auto-approve if configured
      if (config.autoApprove) {
        for (const doc of generatedDocuments) {
          // TODO: Implement auto-approval once approval workflow is implemented
          console.log(`Auto-approving document ${doc.id}`);
        }
      }
    } catch (error) {
      console.error('Error executing generate document action:', error);
      throw error;
    }
  }

  /**
   * Execute notification action
   */
  private async executeSendNotificationAction(
    config: Record<string, any>,
    _event: WorkflowEvent
  ): Promise<void> {
    try {
      const recipients = config.recipients as string[];
      const message = config.message as string;

      console.log(`Sending notification to ${recipients.join(', ')}: ${message}`);

      // TODO: Implement actual notification sending once notification system is implemented
      // For now, just log the notification
    } catch (error) {
      console.error('Error executing send notification action:', error);
      throw error;
    }
  }

  /**
   * Execute status update action
   */
  private async executeUpdateStatusAction(
    config: Record<string, any>,
    event: WorkflowEvent
  ): Promise<void> {
    try {
      const newStatus = config.status as string;
      const entityType = config.entityType as string;
      const entityId = config.entityId || event.procurementId;

      console.log(`Updating ${entityType} ${entityId} status to ${newStatus}`);

      // TODO: Implement actual status update once entity management is implemented
    } catch (error) {
      console.error('Error executing update status action:', error);
      throw error;
    }
  }

  /**
   * Execute task creation action
   */
  private async executeCreateTaskAction(
    config: Record<string, any>,
    event: WorkflowEvent
  ): Promise<void> {
    try {
      const taskTitle = config.title as string;
      const _taskDescription = config.description as string;

      // Use event data for task creation
      console.log(`Creating task for procurement ${event.procurementId}: ${taskTitle} - ${_taskDescription}`);
      const assigneeId = config.assigneeId as string;

      console.log(`Creating task: ${taskTitle} for user ${assigneeId}`);

      // TODO: Implement actual task creation once task management is implemented
    } catch (error) {
      console.error('Error executing create task action:', error);
      throw error;
    }
  }

  /**
   * Get field value from event
   */
  private getEventFieldValue(event: WorkflowEvent, field: string): any {
    const fieldParts = field.split('.');
    let value: any = event;

    for (const part of fieldParts) {
      value = value?.[part];
    }

    return value;
  }

  /**
   * Evaluate condition
   */
  private evaluateCondition(fieldValue: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === expectedValue;
      case 'not_equals':
        return fieldValue !== expectedValue;
      case 'contains':
        return String(fieldValue).includes(String(expectedValue));
      case 'greater_than':
        return Number(fieldValue) > Number(expectedValue);
      case 'less_than':
        return Number(fieldValue) < Number(expectedValue);
      default:
        return true;
    }
  }

  /**
   * Get automation rules
   */
  getAutomationRules(): AutomationRule[] {
    return this.automationRules;
  }

  /**
   * Add automation rule
   */
  addAutomationRule(rule: AutomationRule): void {
    this.automationRules.push(rule);
  }

  /**
   * Update automation rule
   */
  updateAutomationRule(ruleId: string, updates: Partial<AutomationRule>): void {
    const ruleIndex = this.automationRules.findIndex(rule => rule.id === ruleId);
    if (ruleIndex !== -1) {
      this.automationRules[ruleIndex] = {
        ...this.automationRules[ruleIndex],
        ...updates,
      };
    }
  }

  /**
   * Delete automation rule
   */
  deleteAutomationRule(ruleId: string): void {
    this.automationRules = this.automationRules.filter(rule => rule.id !== ruleId);
  }

  /**
   * Enable/disable automation rule
   */
  toggleAutomationRule(ruleId: string, isActive: boolean): void {
    const rule = this.automationRules.find(rule => rule.id === ruleId);
    if (rule) {
      rule.isActive = isActive;
    }
  }
}

export const workflowAutomation = new WorkflowAutomation();
