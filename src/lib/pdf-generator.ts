import Handlebars from "handlebars";
import puppeteer from "puppeteer";

import { uploadFile } from "./upload";
import type {
  TemplateContent,
  TemplateVariable,
} from "./validations/template";

// Extended TemplateComponent interface for PDF generation
export interface PDFTemplateComponent {
  id: string;
  type: 'text' | 'variable' | 'table' | 'list' | 'image' | 'signature' | 'pageBreak' | 'container';
  content?: string;
  style?: {
    fontSize?: number;
    fontWeight?: 'normal' | 'bold';
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    color?: string;
    backgroundColor?: string;
    marginTop?: number;
    marginBottom?: number;
    padding?: number;
    margin?: number;
    borderWidth?: number;
    borderColor?: string;
    borderStyle?: 'solid' | 'dashed' | 'dotted';
  };
  // Variable component properties
  variableName?: string;
  defaultValue?: string;
  format?: 'currency' | 'date' | 'number';
  label?: string;
  // Table component properties
  headers?: string[];
  rows?: string[][];
  variableRows?: string;
  // Image component properties
  src?: string;
  alt?: string;
  // Container properties
  children?: PDFTemplateComponent[];
}

export interface PDFGenerationOptions {
  format?: "A4" | "A3" | "Letter" | "Legal";
  orientation?: "portrait" | "landscape";
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  headerTemplate?: string;
  footerTemplate?: string;
  displayHeaderFooter?: boolean;
}

export interface DocumentData {
  [key: string]: unknown;
}

// PDF-specific template content interface
export interface PDFTemplateContent {
  components: PDFTemplateComponent[];
  pageSettings: {
    size: "A4" | "A3" | "Letter" | "Legal";
    orientation: "portrait" | "landscape";
    margins: {
      top: number;
      right: number;
      bottom: number;
      left: number;
    };
  };
  styles: {
    fontFamily: string;
    fontSize: number;
    lineHeight: number;
  };
}

// Register Handlebars helpers
Handlebars.registerHelper("formatCurrency", function (value: number) {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
});

Handlebars.registerHelper(
  "formatDate",
  function (value: string | Date, format?: string) {
    const date = new Date(value);
    if (format === "short") {
      return date.toLocaleDateString("id-ID");
    }
    return date.toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }
);

Handlebars.registerHelper("formatNumber", function (value: number) {
  return new Intl.NumberFormat("id-ID").format(value);
});

Handlebars.registerHelper("eq", function (a: unknown, b: unknown) {
  return a === b;
});

Handlebars.registerHelper("gt", function (a: number, b: number) {
  return a > b;
});

Handlebars.registerHelper("lt", function (a: number, b: number) {
  return a < b;
});

// Convert template component to HTML
function componentToHTML(
  component: PDFTemplateComponent,
  data: DocumentData
): string {
  switch (component.type) {
    case "text":
      const textStyle = component.style
        ? `style="${styleToCSS(component.style)}"`
        : "";
      return `<div ${textStyle}>${component.content}</div>`;

    case "variable":
      const varStyle = component.style
        ? `style="${styleToCSS(component.style)}"`
        : "";
      const value =
        (component.variableName && data[component.variableName]) || component.defaultValue || "";
      let formattedValue = value;

      if (component.format === "currency" && typeof value === "number") {
        formattedValue = Handlebars.helpers.formatCurrency(value);
      } else if (component.format === "date" && value) {
        formattedValue = Handlebars.helpers.formatDate(value);
      } else if (component.format === "number" && typeof value === "number") {
        formattedValue = Handlebars.helpers.formatNumber(value);
      }

      return `<span ${varStyle}>${formattedValue}</span>`;

    case "table":
      const tableStyle = component.style
        ? `style="${styleToCSS(component.style)}"`
        : "";
      let tableHTML = `<table ${tableStyle} border="1" cellpadding="8" cellspacing="0">`;

      // Headers
      tableHTML += "<thead><tr>";
      component.headers?.forEach((header: string) => {
        tableHTML += `<th>${header}</th>`;
      });
      tableHTML += "</tr></thead>";

      // Rows
      tableHTML += "<tbody>";
      const rows =
        component.variableRows && data[component.variableRows]
          ? data[component.variableRows]
          : component.rows;

      if (Array.isArray(rows)) {
        rows.forEach((row: any) => {
          tableHTML += "<tr>";
          if (Array.isArray(row)) {
            row.forEach((cell: unknown) => {
              tableHTML += `<td>${cell}</td>`;
            });
          } else {
            // If row is an object, map headers to object properties
            component.headers?.forEach((header: string) => {
              const key = header.toLowerCase().replace(/\s+/g, "_");
              tableHTML += `<td>${row[key] || ""}</td>`;
            });
          }
          tableHTML += "</tr>";
        });
      }
      tableHTML += "</tbody></table>";

      return tableHTML;

    case "image":
      const imgStyle = component.style
        ? `style="${styleToCSS(component.style)}"`
        : "";
      const imgSrc =
        component.variableName && data[component.variableName]
          ? data[component.variableName]
          : component.src;

      if (!imgSrc) return "";

      return `<img src="${imgSrc}" alt="${component.alt || ""}" ${imgStyle} />`;

    case "signature":
      const sigStyle = component.style
        ? `style="${styleToCSS(component.style)}"`
        : "";
      const sigValue =
        component.variableName && data[component.variableName]
          ? data[component.variableName]
          : "";

      return `
        <div ${sigStyle}>
          <div style="border: 1px solid #ccc; height: 100px; width: 200px; margin-bottom: 10px;">
            ${
              sigValue
                ? `<img src="${sigValue}" style="max-width: 100%; max-height: 100%;" />`
                : ""
            }
          </div>
          <div style="text-align: center; font-size: 12px;">${
            component.label
          }</div>
        </div>
      `;

    case "pageBreak":
      return '<div style="page-break-before: always;"></div>';

    case "container":
      const containerStyle = component.style
        ? `style="${styleToCSS(component.style)}"`
        : "";
      let containerHTML = `<div ${containerStyle}>`;
      component.children?.forEach((child: PDFTemplateComponent) => {
        containerHTML += componentToHTML(child, data);
      });
      containerHTML += "</div>";
      return containerHTML;

    default:
      return "";
  }
}

// Convert style object to CSS string
function styleToCSS(style: Record<string, unknown>): string {
  const cssRules: string[] = [];

  if (style.fontSize) cssRules.push(`font-size: ${style.fontSize}px`);
  if (style.fontWeight) cssRules.push(`font-weight: ${style.fontWeight}`);
  if (style.textAlign) cssRules.push(`text-align: ${style.textAlign}`);
  if (style.color) cssRules.push(`color: ${style.color}`);
  if (style.backgroundColor)
    cssRules.push(`background-color: ${style.backgroundColor}`);
  if (style.marginTop) cssRules.push(`margin-top: ${style.marginTop}px`);
  if (style.marginBottom)
    cssRules.push(`margin-bottom: ${style.marginBottom}px`);
  if (style.padding) cssRules.push(`padding: ${style.padding}px`);
  if (style.margin) cssRules.push(`margin: ${style.margin}px`);
  if (style.borderWidth) cssRules.push(`border-width: ${style.borderWidth}px`);
  if (style.borderColor) cssRules.push(`border-color: ${style.borderColor}`);
  if (style.borderStyle) cssRules.push(`border-style: ${style.borderStyle}`);
  if (style.borderRadius)
    cssRules.push(`border-radius: ${style.borderRadius}px`);
  if (style.width) cssRules.push(`width: ${style.width}px`);
  if (style.height) cssRules.push(`height: ${style.height}px`);

  return cssRules.join("; ");
}

// Generate HTML from template content and data
export function generateHTML(
  content: PDFTemplateContent,
  data: DocumentData,
  variables: TemplateVariable[]
): string {
  // Validate data against variables
  const validatedData = { ...data };
  variables.forEach((variable) => {
    if (variable.required && !validatedData[variable.name]) {
      throw new Error(`Required variable '${variable.name}' is missing`);
    }

    // Apply default values
    if (!validatedData[variable.name] && variable.defaultValue !== undefined) {
      validatedData[variable.name] = variable.defaultValue;
    }
  });

  // Generate HTML for each component
  let bodyHTML = "";
  content.components.forEach((component) => {
    bodyHTML += componentToHTML(component, validatedData);
  });

  // Create complete HTML document
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Generated Document</title>
      <style>
        body {
          font-family: ${content.styles.fontFamily};
          font-size: ${content.styles.fontSize}px;
          line-height: ${content.styles.lineHeight};
          margin: 0;
          padding: 20px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
        }
        th, td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f2f2f2;
          font-weight: bold;
        }
        .page-break {
          page-break-before: always;
        }
        @media print {
          body {
            margin: 0;
          }
        }
      </style>
    </head>
    <body>
      ${bodyHTML}
    </body>
    </html>
  `;

  return html;
}

// Generate PDF from template content and data
export async function generatePDF(
  content: PDFTemplateContent,
  data: DocumentData,
  variables: TemplateVariable[],
  options: PDFGenerationOptions = {}
): Promise<{ url: string; buffer: Buffer }> {
  let browser;

  try {
    // Generate HTML
    const html = generateHTML(content, data, variables);

    // Launch Puppeteer
    browser = await puppeteer.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const page = await browser.newPage();

    // Set content
    await page.setContent(html, { waitUntil: "networkidle0" });

    // Configure PDF options
    const pdfOptions: any = {
      format: options.format || content.pageSettings.size,
      landscape:
        (options.orientation || content.pageSettings.orientation) ===
        "landscape",
      margin: {
        top: `${options.margins?.top || content.pageSettings.margins.top}mm`,
        right: `${
          options.margins?.right || content.pageSettings.margins.right
        }mm`,
        bottom: `${
          options.margins?.bottom || content.pageSettings.margins.bottom
        }mm`,
        left: `${options.margins?.left || content.pageSettings.margins.left}mm`,
      },
      printBackground: true,
    };

    if (options.displayHeaderFooter) {
      pdfOptions.displayHeaderFooter = true;
      pdfOptions.headerTemplate = options.headerTemplate || "";
      pdfOptions.footerTemplate = options.footerTemplate || "";
    }

    // Generate PDF
    const pdfBuffer = await page.pdf(pdfOptions);

    // Upload PDF to storage
    const pdfBufferAsBuffer = Buffer.from(pdfBuffer);
    const pdfFile = new File([pdfBufferAsBuffer], "document.pdf", {
      type: "application/pdf",
    });
    const uploadResult = await uploadFile(pdfFile);

    return {
      url: uploadResult.url,
      buffer: pdfBufferAsBuffer,
    };
  } catch (error) {
    console.error("PDF generation error:", error);
    throw new Error("Failed to generate PDF");
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Preview HTML (for development/testing)
export function previewHTML(
  content: TemplateContent,
  data: DocumentData,
  variables: TemplateVariable[]
): string {
  return generateHTML(content, data, variables);
}
