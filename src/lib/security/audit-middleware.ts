import { NextRequest, NextResponse } from "next/server";

import { getCurrentUser } from "@/lib/auth";
import { prisma } from "@/lib/db";

export interface AuditOptions {
  action: string; // e.g., "CREATE", "UPDATE", "DELETE", "READ"
  resource: string; // e.g., "procurement", "vendor", "offer"
  resourceId?: string | ((request: NextRequest, ...args: any[]) => string);
  description?: string | ((request: NextRequest, ...args: any[]) => string);
  severity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  category?: string; // e.g., "PROCUREMENT", "VENDOR_MANAGEMENT", "SECURITY"
  captureRequest?: boolean; // Whether to capture request body
  captureResponse?: boolean; // Whether to capture response body
  skipAuditIf?: (request: NextRequest, ...args: any[]) => boolean; // Skip audit under certain conditions
}

// Extract client information from request
function getClientInfo(request: NextRequest) {
  return {
    ipAddress: request.headers.get("x-forwarded-for") || 
               request.headers.get("x-real-ip") || 
               request.headers.get("cf-connecting-ip") ||
               "unknown",
    userAgent: request.headers.get("user-agent") || "unknown",
    referer: request.headers.get("referer") || null,
    origin: request.headers.get("origin") || null,
  };
}

// Create audit log entry
async function createAuditLog(
  options: AuditOptions,
  request: NextRequest,
  response?: NextResponse,
  user?: any,
  args?: any[]
): Promise<void> {
  try {
    const clientInfo = getClientInfo(request);
    
    // Resolve dynamic values
    const resourceId = typeof options.resourceId === "function" 
      ? options.resourceId(request, ...(args || []))
      : options.resourceId;
      
    const description = typeof options.description === "function"
      ? options.description(request, ...(args || []))
      : options.description || `${options.action} ${options.resource}`;

    // Prepare metadata
    const metadata: any = {
      method: request.method,
      url: request.url,
      ...clientInfo,
    };

    // Capture request data if requested
    if (options.captureRequest && request.method !== "GET") {
      try {
        // Create a clone of the request to avoid consuming the body
        const requestClone = new Request(request.url, {
          method: request.method,
          headers: request.headers,
          body: (request as any).body,
        });
        const contentType = request.headers.get("content-type");

        if (contentType?.includes("application/json")) {
          metadata.requestBody = await requestClone.json();
        } else if (contentType?.includes("application/x-www-form-urlencoded")) {
          const formData = await requestClone.formData();
          metadata.requestBody = Object.fromEntries(formData.entries());
        }
      } catch (error) {
        metadata.requestCaptureError = `Failed to capture request body: ${(error as Error).message}`;
      }
    }

    // Capture response data if requested
    if (options.captureResponse && response) {
      try {
        metadata.responseStatus = response.status;
        metadata.responseHeaders = Object.fromEntries(response.headers.entries());
      } catch (error) {
        metadata.responseCaptureError = `Failed to capture response data: ${(error as Error).message}`;
      }
    }

    // Add query parameters
    const url = new URL(request.url);
    if (url.searchParams.toString()) {
      metadata.queryParams = Object.fromEntries(url.searchParams.entries());
    }

    // Create audit log entry
    await prisma.auditLog.create({
      data: {
        userId: user?.id || null,
        sessionId: request.headers.get("x-session-id") || null,
        action: options.action as any, // Cast to enum
        resource: options.resource,
        resourceId,
        description,
        metadata,
        severity: options.severity || "MEDIUM",
        category: (options.category as any) || "GENERAL",
        ipAddress: clientInfo.ipAddress,
        userAgent: clientInfo.userAgent,
      },
    });
  } catch (_error) {
    // Log audit failure but don't break the request
    console.error("Failed to create audit log:", _error);
  }
}

// Audit middleware factory
export function withAudit(options: AuditOptions) {
  return function auditMiddleware<T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest, ...args: T): Promise<NextResponse> {
      let user: any = null;
      let response: NextResponse | null = null;
      let error: any = null;

      try {
        // Get current user (if authenticated)
        try {
          user = await getCurrentUser(request);
        } catch (authError) {
          // User might not be authenticated for some endpoints
          console.warn("Authentication failed during audit:", (authError as Error).message);
          user = null;
        }

        // Check if we should skip audit
        if (options.skipAuditIf && options.skipAuditIf(request, ...args)) {
          return handler(request, ...args);
        }

        // Execute the handler
        response = await handler(request, ...args);

        // Create audit log for successful requests
        await createAuditLog(options, request, response, user, args);

        return response;
      } catch (handlerError) {
        error = handlerError;

        // Create audit log for failed requests
        await createAuditLog(
          {
            ...options,
            action: `${options.action}_FAILED`,
            description: `Failed to ${options.action.toLowerCase()} ${options.resource}: ${error.message}`,
            severity: "HIGH",
          },
          request,
          undefined,
          user,
          args
        );

        throw error;
      }
    };
  };
}

// Specific audit middleware for common actions
export function auditCreate(resource: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action: "CREATE",
    resource,
    severity: "MEDIUM",
    captureRequest: true,
    ...options,
  });
}

export function auditUpdate(resource: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action: "UPDATE",
    resource,
    severity: "MEDIUM",
    captureRequest: true,
    ...options,
  });
}

export function auditDelete(resource: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action: "DELETE",
    resource,
    severity: "HIGH",
    captureRequest: true,
    ...options,
  });
}

export function auditRead(resource: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action: "READ",
    resource,
    severity: "LOW",
    captureRequest: false,
    ...options,
  });
}

// Security-specific audit middleware
export function auditSecurity(action: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action,
    resource: "security",
    severity: "HIGH",
    category: "SECURITY",
    captureRequest: true,
    ...options,
  });
}

// Authentication audit middleware
export function auditAuth(action: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action,
    resource: "authentication",
    severity: "MEDIUM",
    category: "AUTHENTICATION",
    captureRequest: true,
    ...options,
  });
}

// Procurement-specific audit middleware
export function auditProcurement(action: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action,
    resource: "procurement",
    category: "PROCUREMENT",
    captureRequest: true,
    ...options,
  });
}

// Vendor-specific audit middleware
export function auditVendor(action: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action,
    resource: "vendor",
    category: "VENDOR_MANAGEMENT",
    captureRequest: true,
    ...options,
  });
}

// Financial audit middleware
export function auditFinancial(action: string, options?: Partial<AuditOptions>) {
  return withAudit({
    action,
    resource: "financial",
    severity: "HIGH",
    category: "FINANCIAL",
    captureRequest: true,
    captureResponse: true,
    ...options,
  });
}
